import Head from 'next/head';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'service';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
}

const defaultSEO = {
  title: 'Lunar Cubes - Digital Marketing Agency in Nepal | From Concept to Cosmos',
  description: 'Leading digital marketing agency in Nepal helping SMEs grow online. Expert services in social media marketing, SEO, web development, and Google Ads. Serving Kathmandu, Pokhara & beyond.',
  keywords: 'digital marketing Nepal, SEO Nepal, social media marketing Kathmandu, web development Nepal, Google Ads Nepal, digital agency Kathmandu',
  image: '/images/og-image.jpg',
  url: 'https://lunarcubes.com.np',
  type: 'website' as const,
};

export default function SEO({
  title,
  description = defaultSEO.description,
  keywords = defaultSEO.keywords,
  image = defaultSEO.image,
  url = defaultSEO.url,
  type = defaultSEO.type,
  author,
  publishedTime,
  modifiedTime,
}: SEOProps) {
  const fullTitle = title 
    ? `${title} | Lunar Cubes - Digital Marketing Nepal`
    : defaultSEO.title;

  const fullImageUrl = image.startsWith('http') ? image : `${url}${image}`;

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content={author || 'Lunar Cubes Digital Marketing'} />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="robots" content="index, follow" />
      <meta name="language" content="English" />
      <meta name="revisit-after" content="7 days" />

      {/* Open Graph Meta Tags */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImageUrl} />
      <meta property="og:url" content={url} />
      <meta property="og:site_name" content="Lunar Cubes" />
      <meta property="og:locale" content="en_US" />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImageUrl} />
      <meta name="twitter:site" content="@lunarcubes" />
      <meta name="twitter:creator" content="@lunarcubes" />

      {/* Article specific meta tags */}
      {type === 'article' && publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {type === 'article' && modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      {type === 'article' && author && (
        <meta property="article:author" content={author} />
      )}

      {/* Canonical URL */}
      <link rel="canonical" href={url} />

      {/* Favicon */}
      <link rel="icon" href="/favicon.ico" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="manifest" href="/site.webmanifest" />

      {/* Additional SEO Meta Tags */}
      <meta name="theme-color" content="#1e40af" />
      <meta name="msapplication-TileColor" content="#1e40af" />
      
      {/* Structured Data for Local Business */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "LocalBusiness",
            "name": "Lunar Cubes",
            "description": "Digital Marketing Agency in Nepal",
            "url": "https://lunarcubes.com.np",
            "telephone": "+977-1-4441234",
            "address": {
              "@type": "PostalAddress",
              "streetAddress": "Thamel",
              "addressLocality": "Kathmandu",
              "addressCountry": "Nepal"
            },
            "geo": {
              "@type": "GeoCoordinates",
              "latitude": 27.7172,
              "longitude": 85.3240
            },
            "openingHours": "Mo-Fr 09:00-18:00",
            "sameAs": [
              "https://facebook.com/lunarcubesnepal",
              "https://instagram.com/lunarcubes",
              "https://linkedin.com/company/lunar-cubes"
            ],
            "priceRange": "$$",
            "image": fullImageUrl,
            "logo": `${url}/images/logo.png`
          })
        }}
      />
    </Head>
  );
}
