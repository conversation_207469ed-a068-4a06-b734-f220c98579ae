<svg width="300" height="300" viewBox="0 0 300 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="teamGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="300" height="300" fill="url(#teamGradient1)"/>
  
  <!-- Avatar Circle -->
  <circle cx="150" cy="120" r="60" fill="white" opacity="0.2"/>
  <circle cx="150" cy="120" r="50" fill="white"/>
  
  <!-- Initials -->
  <text x="150" y="135" text-anchor="middle" fill="#1e40af" font-family="Arial, sans-serif" font-size="32" font-weight="bold">AS</text>
  
  <!-- Name and Title -->
  <text x="150" y="220" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" font-weight="bold">Ashish Sharma</text>
  <text x="150" y="245" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" opacity="0.9">CEO & Founder</text>
  
  <!-- Decorative Elements -->
  <circle cx="50" cy="50" r="3" fill="white" opacity="0.6"/>
  <circle cx="250" cy="50" r="2" fill="white" opacity="0.6"/>
  <circle cx="50" cy="250" r="2" fill="white" opacity="0.6"/>
  <circle cx="250" cy="250" r="3" fill="white" opacity="0.6"/>
</svg>
