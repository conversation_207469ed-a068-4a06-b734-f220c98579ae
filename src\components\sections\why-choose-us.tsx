'use client';

import { motion } from 'framer-motion';
import { 
  MapPin, 
  Users, 
  TrendingUp, 
  Shield, 
  Clock, 
  Award,
  CheckCircle,
  Star
} from 'lucide-react';

const features = [
  {
    icon: MapPin,
    title: 'Local Market Expertise',
    description: 'Deep understanding of Nepali consumer behavior, cultural nuances, and market dynamics that drive successful campaigns.',
    stats: '5+ years in Nepal market'
  },
  {
    icon: Users,
    title: 'Dedicated Team',
    description: 'Experienced professionals who speak your language and understand your business challenges in the local context.',
    stats: '12+ expert team members'
  },
  {
    icon: TrendingUp,
    title: 'Proven Results',
    description: 'Track record of delivering measurable growth for 150+ Nepali businesses across various industries.',
    stats: '300+ successful projects'
  },
  {
    icon: Shield,
    title: 'Transparent Approach',
    description: 'Clear communication, detailed reporting, and honest advice. No hidden costs or unrealistic promises.',
    stats: '100% transparency guarantee'
  },
  {
    icon: Clock,
    title: 'Quick Response',
    description: 'Fast turnaround times and responsive support. We understand the pace of business in Nepal.',
    stats: '24-hour response time'
  },
  {
    icon: Award,
    title: 'Quality Assurance',
    description: 'Rigorous quality checks and continuous optimization to ensure your campaigns deliver maximum ROI.',
    stats: '98% client satisfaction'
  }
];

const achievements = [
  {
    number: '150+',
    label: 'Happy Clients',
    description: 'Businesses transformed'
  },
  {
    number: '300+',
    label: 'Projects Completed',
    description: 'Successful campaigns'
  },
  {
    number: '500%',
    label: 'Average Growth',
    description: 'In digital presence'
  },
  {
    number: '5+',
    label: 'Years Experience',
    description: 'In Nepal market'
  }
];

export default function WhyChooseUs() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center px-4 py-2 bg-brand-gold/10 text-brand-navy rounded-full text-sm font-medium mb-6">
            <Star className="w-4 h-4 mr-2 text-brand-gold" />
            Why Choose Lunar Cubes
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Your Success is Our
            <span className="block text-brand-navy">Mission</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            We&apos;re not just another digital marketing agency. We&apos;re your partners in growth, 
            committed to understanding your unique challenges and delivering solutions that work in Nepal.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Left Content - Features */}
          <div>
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="mb-8"
            >
              <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                What Makes Us Different
              </h3>
              <p className="text-gray-600 leading-relaxed">
                Our unique combination of global digital marketing expertise and deep local market 
                knowledge sets us apart from other agencies in Nepal.
              </p>
            </motion.div>

            <div className="space-y-6">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-start space-x-4 group"
                >
                  <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-brand-navy to-brand-navy-dark rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <feature.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">
                      {feature.title}
                    </h4>
                    <p className="text-gray-600 mb-2 leading-relaxed">
                      {feature.description}
                    </p>
                    <div className="text-sm font-medium text-brand-navy">
                      {feature.stats}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Right Content - Visual */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            {/* Main Card */}
            <div className="bg-gradient-to-br from-brand-navy to-brand-navy-dark rounded-3xl p-8 text-white relative overflow-hidden">
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-0 right-0 w-32 h-32 bg-white rounded-full -translate-y-16 translate-x-16"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-brand-gold rounded-full translate-y-12 -translate-x-12"></div>
              </div>

              <div className="relative z-10">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-brand-gold rounded-full flex items-center justify-center mr-4">
                    <Award className="h-6 w-6 text-gray-900" />
                  </div>
                  <div>
                    <h4 className="text-xl font-bold">Certified Excellence</h4>
                    <p className="text-blue-100">Google & Facebook Partners</p>
                  </div>
                </div>

                <div className="space-y-4 mb-6">
                  {[
                    'Google Ads Certified Professionals',
                    'Facebook Marketing Partners',
                    'HubSpot Certified Agency',
                    'Local Business Specialists'
                  ].map((certification, index) => (
                    <motion.div
                      key={certification}
                      initial={{ opacity: 0, x: 20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                      viewport={{ once: true }}
                      className="flex items-center"
                    >
                      <CheckCircle className="h-5 w-5 text-brand-gold mr-3 flex-shrink-0" />
                      <span className="text-blue-100">{certification}</span>
                    </motion.div>
                  ))}
                </div>

                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-sm text-blue-100 mb-2">Client Satisfaction Rate</div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-2xl font-bold">98%</span>
                    <div className="flex space-x-1">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-brand-gold fill-current" />
                      ))}
                    </div>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <motion.div
                      initial={{ width: 0 }}
                      whileInView={{ width: '98%' }}
                      transition={{ duration: 2, delay: 1 }}
                      viewport={{ once: true }}
                      className="bg-brand-gold h-2 rounded-full"
                    ></motion.div>
                  </div>
                </div>
              </div>
            </div>

            {/* Floating Achievement Cards */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              viewport={{ once: true }}
              className="absolute -top-6 -left-6 bg-white rounded-2xl p-4 shadow-xl border"
            >
              <div className="text-2xl font-bold text-brand-navy">150+</div>
              <div className="text-sm text-gray-600">Happy Clients</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 1 }}
              viewport={{ once: true }}
              className="absolute -bottom-6 -right-6 bg-brand-gold rounded-2xl p-4 shadow-xl"
            >
              <div className="text-2xl font-bold text-gray-900">5+</div>
              <div className="text-sm text-gray-700">Years Experience</div>
            </motion.div>
          </motion.div>
        </div>

        {/* Achievements Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-4 gap-8"
        >
          {achievements.map((achievement, index) => (
            <motion.div
              key={achievement.label}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="text-3xl md:text-4xl font-bold text-brand-navy mb-2">
                {achievement.number}
              </div>
              <div className="text-lg font-semibold text-gray-900 mb-1">
                {achievement.label}
              </div>
              <div className="text-sm text-gray-600">
                {achievement.description}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
