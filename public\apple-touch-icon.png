<!-- This would be a PNG file in production. Creating SVG placeholder -->
<svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="appleIconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="180" height="180" rx="40" fill="url(#appleIconGradient)"/>
  
  <!-- LC Letters -->
  <text x="90" y="110" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="60" font-weight="bold">LC</text>
  
  <!-- Small decorative elements -->
  <circle cx="45" cy="45" r="4" fill="white" opacity="0.6"/>
  <circle cx="135" cy="45" r="4" fill="white" opacity="0.6"/>
  <circle cx="45" cy="135" r="4" fill="white" opacity="0.6"/>
  <circle cx="135" cy="135" r="4" fill="white" opacity="0.6"/>
</svg>
