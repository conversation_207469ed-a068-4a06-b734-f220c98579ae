'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowLeft, MapPin, Clock, Users, DollarSign, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface JobPosting {
  id: string;
  slug: string;
  title: string;
  department: string;
  location: string;
  type: 'full-time' | 'part-time' | 'contract' | 'internship';
  status: 'active' | 'closed' | 'draft';
  publishedAt: string;
  updatedAt?: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
  salary?: {
    min: number;
    max: number;
    currency: string;
    period: string;
  };
  duration?: string;
}

interface JobPostingHeaderProps {
  job: JobPosting;
}

const jobTypeColors = {
  'full-time': 'bg-green-100 text-green-800',
  'part-time': 'bg-blue-100 text-blue-800',
  'contract': 'bg-purple-100 text-purple-800',
  'internship': 'bg-orange-100 text-orange-800',
};

const departmentColors: Record<string, string> = {
  'Engineering': 'bg-blue-500',
  'Marketing': 'bg-green-500',
  'Design': 'bg-purple-500',
  'Sales': 'bg-orange-500',
  'Operations': 'bg-teal-500',
};

export default function JobPostingHeader({ job }: JobPostingHeaderProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <section className="relative bg-gradient-to-br from-brand-navy via-brand-navy-dark to-gray-900 text-white py-20">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Back Button */}
        <motion.div
          initial={{ opacity: 0, x: -30 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <Button
            variant="outline"
            size="sm"
            className="bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white hover:text-gray-900"
            asChild
          >
            <Link href="/careers">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Careers
            </Link>
          </Button>
        </motion.div>

        <div className="max-w-4xl">
          {/* Department Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="flex items-center gap-4 mb-6"
          >
            <div
              className={`w-4 h-4 rounded-full ${departmentColors[job.department] || 'bg-gray-500'}`}
            ></div>
            <span className="text-brand-gold font-medium">{job.department}</span>
            <Badge
              variant="secondary"
              className={`${jobTypeColors[job.type]} border-0 font-medium`}
            >
              {job.type.charAt(0).toUpperCase() + job.type.slice(1).replace('-', ' ')}
            </Badge>
          </motion.div>

          {/* Job Title */}
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-4xl md:text-6xl font-bold mb-6 font-heading"
          >
            {job.title}
          </motion.h1>

          {/* Job Meta Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8"
          >
            <div className="flex items-center gap-3">
              <MapPin className="h-5 w-5 text-brand-gold" />
              <div>
                <p className="text-sm text-gray-300">Location</p>
                <p className="font-medium">{job.location}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Clock className="h-5 w-5 text-brand-gold" />
              <div>
                <p className="text-sm text-gray-300">Type</p>
                <p className="font-medium">{job.type.replace('-', ' ')}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Users className="h-5 w-5 text-brand-gold" />
              <div>
                <p className="text-sm text-gray-300">Department</p>
                <p className="font-medium">{job.department}</p>
              </div>
            </div>

            {job.salary && (
              <div className="flex items-center gap-3">
                <DollarSign className="h-5 w-5 text-brand-gold" />
                <div>
                  <p className="text-sm text-gray-300">Salary</p>
                  <p className="font-medium">
                    {job.salary.currency} {job.salary.min.toLocaleString()} - {job.salary.max.toLocaleString()}
                  </p>
                </div>
              </div>
            )}
          </motion.div>

          {/* Job Description Preview */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-xl text-gray-300 mb-8 max-w-3xl"
          >
            {job.description.length > 200
              ? `${job.description.substring(0, 200)}...`
              : job.description
            }
          </motion.p>

          {/* Posted Date */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="flex items-center gap-3 text-sm text-gray-400"
          >
            <Calendar className="h-4 w-4" />
            <span>Posted on {formatDate(job.publishedAt)}</span>
            {job.updatedAt && job.updatedAt !== job.publishedAt && (
              <>
                <span>•</span>
                <span>Updated on {formatDate(job.updatedAt)}</span>
              </>
            )}
          </motion.div>

          {/* Quick Apply Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="mt-8"
          >
            <Button
              size="lg"
              className="bg-brand-gold hover:bg-yellow-500 text-brand-navy px-8 py-4 text-lg font-semibold"
              onClick={() => {
                const applicationSection = document.getElementById('application-form');
                applicationSection?.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              Apply for This Position
            </Button>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
