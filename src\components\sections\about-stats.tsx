'use client';

import { motion } from 'framer-motion';
import { TrendingUp, Users, Award, MapPin, Clock, Star } from 'lucide-react';
import { useCompanyInfo } from '@/lib/queries/hooks';

export default function AboutStats() {
  const { data: company } = useCompanyInfo();

  const achievements = [
    {
      icon: Users,
      value: company?.stats.clientsServed || 150,
      suffix: '+',
      label: 'Happy Clients',
      description: 'Businesses transformed across Nepal'
    },
    {
      icon: Award,
      value: company?.stats.projectsCompleted || 300,
      suffix: '+',
      label: 'Projects Completed',
      description: 'Successful digital campaigns delivered'
    },
    {
      icon: TrendingUp,
      value: 500,
      suffix: '%',
      label: 'Average Growth',
      description: 'Increase in digital presence'
    },
    {
      icon: Clock,
      value: company?.stats.yearsExperience || 5,
      suffix: '+',
      label: 'Years Experience',
      description: 'Serving the Nepali market'
    },
    {
      icon: Star,
      value: 98,
      suffix: '%',
      label: 'Client Satisfaction',
      description: 'Based on client feedback surveys'
    },
    {
      icon: MapPin,
      value: 7,
      suffix: '',
      label: 'Cities Served',
      description: 'Across Nepal and beyond'
    }
  ];

  const milestones = [
    {
      year: '2019',
      title: 'Company Founded',
      description: 'Started our journey in Kathmandu'
    },
    {
      year: '2020',
      title: 'First 50 Clients',
      description: 'Reached our first major milestone'
    },
    {
      year: '2021',
      title: 'Team Expansion',
      description: 'Grew to 8+ team members'
    },
    {
      year: '2022',
      title: '100+ Projects',
      description: 'Completed our 100th project'
    },
    {
      year: '2023',
      title: 'Industry Recognition',
      description: 'Recognized as leading agency'
    },
    {
      year: '2024',
      title: 'Market Leader',
      description: 'Established as Nepal\'s premier agency'
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center px-4 py-2 bg-brand-navy/10 text-brand-navy rounded-full text-sm font-medium mb-6">
            <TrendingUp className="w-4 h-4 mr-2 text-brand-gold" />
            Our Impact
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Numbers That Tell
            <span className="block text-brand-navy">Our Story</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            These numbers represent more than just statistics – they represent the trust our clients 
            have placed in us and the impact we&apos;ve made on Nepal&apos;s digital landscape.
          </p>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {achievements.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 text-center h-full">
                <div className="w-16 h-16 bg-gradient-to-br from-brand-navy to-brand-gold rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <stat.icon className="h-8 w-8 text-white" />
                </div>
                
                <div className="text-4xl md:text-5xl font-bold text-brand-navy mb-2">
                  <motion.span
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 1, delay: 0.5 + index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    {stat.value}{stat.suffix}
                  </motion.span>
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {stat.label}
                </h3>
                
                <p className="text-gray-600 text-sm">
                  {stat.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Timeline Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-white rounded-3xl p-8 md:p-12 mb-16"
        >
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Our Journey Through the Years
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              From a small startup to Nepal&apos;s leading digital marketing agency, 
              here are the key milestones that shaped our journey.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {milestones.map((milestone, index) => (
              <motion.div
                key={milestone.year}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center p-6 rounded-xl bg-gray-50 hover:bg-brand-navy hover:text-white transition-all duration-300 group"
              >
                <div className="text-2xl font-bold text-brand-gold mb-2 group-hover:text-brand-gold">
                  {milestone.year}
                </div>
                <h4 className="text-lg font-semibold mb-2 group-hover:text-white">
                  {milestone.title}
                </h4>
                <p className="text-sm text-gray-600 group-hover:text-blue-100">
                  {milestone.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Recognition Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-brand-navy to-brand-navy-dark rounded-3xl p-8 md:p-12 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-6">
              Trusted by Leading Businesses
            </h3>
            <p className="text-blue-100 mb-8 max-w-2xl mx-auto">
              Our success is measured by the success of our clients. We&apos;re proud to have worked 
              with some of Nepal&apos;s most innovative and forward-thinking businesses.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {[
                { label: 'Hospitality', count: '40+' },
                { label: 'E-commerce', count: '35+' },
                { label: 'Healthcare', count: '25+' },
                { label: 'Technology', count: '20+' }
              ].map((industry, index) => (
                <motion.div
                  key={industry.label}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="text-3xl font-bold text-brand-gold mb-2">
                    {industry.count}
                  </div>
                  <div className="text-blue-100">
                    {industry.label} Clients
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
