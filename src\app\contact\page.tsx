import { Metadata } from 'next';
import ContactHero from '@/components/sections/contact-hero';
import ContactForm from '@/components/sections/contact-form';
import ContactInfo from '@/components/sections/contact-info';

export const metadata: Metadata = {
  title: 'Contact Us - Lunar Cubes Digital Marketing Nepal',
  description: 'Get in touch with Lunar Cubes for your digital marketing needs. Located in Kathmandu, Nepal. Call +977-1-4441234 <NAME_EMAIL> for free consultation.',
  keywords: 'contact lunar cubes, digital marketing consultation Nepal, Kathmandu digital agency contact, free consultation Nepal',
  openGraph: {
    title: 'Contact Lunar Cubes - Digital Marketing Agency Nepal',
    description: 'Ready to grow your business online? Contact Nepal\'s leading digital marketing agency for a free consultation.',
    url: '/contact',
  },
};

export default function ContactPage() {
  return (
    <div>
      <ContactHero />
      <ContactForm />
      <ContactInfo />
    </div>
  );
}
