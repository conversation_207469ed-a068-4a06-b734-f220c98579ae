'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRight, CheckCircle, Star, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';

const serviceHighlights = [
  'Tailored for Nepali Market',
  'Proven Track Record',
  'Expert Team',
  'Transparent Pricing',
  'Measurable Results',
  '24/7 Support'
];

export default function ServicesHero() {
  return (
    <section className="relative py-20 bg-gradient-to-br from-blue-50 via-white to-yellow-50 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-20 w-64 h-64 bg-brand-gold/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-64 h-64 bg-brand-navy/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center px-4 py-2 bg-brand-navy/10 text-brand-navy rounded-full text-sm font-medium mb-6"
          >
            <Zap className="w-4 h-4 mr-2 text-brand-gold" />
            Our Services
          </motion.div>

          {/* Main Heading */}
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight"
          >
            Complete Digital Marketing
            <span className="block bg-gradient-to-r from-brand-navy to-brand-gold bg-clip-text text-transparent">
              Solutions for Nepal
            </span>
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-lg md:text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed"
          >
            From social media management to advanced SEO strategies, we offer comprehensive 
            digital marketing services designed specifically for Nepali businesses. 
            Let us help you dominate your market online.
          </motion.p>

          {/* Service Highlights */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8 max-w-2xl mx-auto"
          >
            {serviceHighlights.map((highlight, index) => (
              <motion.div
                key={highlight}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.7 + index * 0.1 }}
                className="flex items-center space-x-2 text-sm text-gray-700"
              >
                <CheckCircle className="h-4 w-4 text-brand-gold flex-shrink-0" />
                <span>{highlight}</span>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
          >
            <Button 
              asChild 
              size="lg" 
              className="bg-brand-navy hover:bg-brand-navy-dark text-white px-8 py-4 text-lg font-semibold group"
            >
              <Link href="/contact">
                Get Free Consultation
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
            
            <Button 
              asChild
              variant="outline" 
              size="lg" 
              className="border-brand-navy text-brand-navy hover:bg-brand-navy hover:text-white px-8 py-4 text-lg font-semibold"
            >
              <Link href="#services">
                Explore Services
              </Link>
            </Button>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1 }}
            className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100"
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="flex justify-center mb-3">
                  <Star className="h-8 w-8 text-brand-gold" />
                </div>
                <div className="text-2xl font-bold text-brand-navy mb-2">150+</div>
                <div className="text-gray-600">Happy Clients</div>
              </div>
              
              <div className="text-center">
                <div className="flex justify-center mb-3">
                  <CheckCircle className="h-8 w-8 text-brand-gold" />
                </div>
                <div className="text-2xl font-bold text-brand-navy mb-2">300+</div>
                <div className="text-gray-600">Projects Completed</div>
              </div>
              
              <div className="text-center">
                <div className="flex justify-center mb-3">
                  <Zap className="h-8 w-8 text-brand-gold" />
                </div>
                <div className="text-2xl font-bold text-brand-navy mb-2">500%</div>
                <div className="text-gray-600">Average Growth</div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
