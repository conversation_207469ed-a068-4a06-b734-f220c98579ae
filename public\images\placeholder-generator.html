<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lunar Cubes - Image Placeholder Generator</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e40af, #f59e0b);
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .placeholder {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            margin: 20px 0;
            padding: 40px;
            backdrop-filter: blur(10px);
        }
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #1e40af, #f59e0b);
            border-radius: 20px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: bold;
            color: white;
        }
        h1 { font-size: 2.5em; margin-bottom: 10px; }
        h2 { font-size: 1.8em; margin-bottom: 20px; }
        .tagline { font-size: 1.2em; opacity: 0.9; margin-bottom: 30px; }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .team-member {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .avatar {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #f59e0b, #1e40af);
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
        }
        .portfolio-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin: 20px 0;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .stat {
            text-align: center;
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #f59e0b;
        }
        .note {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
            border-left: 4px solid #f59e0b;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Hero Section -->
        <div class="placeholder">
            <div class="logo">LC</div>
            <h1>Lunar Cubes</h1>
            <p class="tagline">From Concept to Cosmos</p>
            <p>Nepal's Premier Digital Marketing Agency</p>
            <div class="stats">
                <div class="stat">
                    <div class="stat-number">150+</div>
                    <div>Happy Clients</div>
                </div>
                <div class="stat">
                    <div class="stat-number">300+</div>
                    <div>Projects</div>
                </div>
                <div class="stat">
                    <div class="stat-number">5+</div>
                    <div>Years</div>
                </div>
                <div class="stat">
                    <div class="stat-number">98%</div>
                    <div>Success Rate</div>
                </div>
            </div>
        </div>

        <!-- Services Section -->
        <div class="placeholder">
            <h2>Our Services</h2>
            <div class="features">
                <div class="feature">
                    <h3>📱 Social Media Marketing</h3>
                    <p>Engage your audience across all platforms</p>
                </div>
                <div class="feature">
                    <h3>🔍 SEO Optimization</h3>
                    <p>Rank higher on search engines</p>
                </div>
                <div class="feature">
                    <h3>💻 Web Development</h3>
                    <p>Beautiful, responsive websites</p>
                </div>
                <div class="feature">
                    <h3>🎯 Google Ads</h3>
                    <p>Targeted advertising campaigns</p>
                </div>
                <div class="feature">
                    <h3>✍️ Content Marketing</h3>
                    <p>Compelling content that converts</p>
                </div>
                <div class="feature">
                    <h3>🎨 Brand Identity</h3>
                    <p>Memorable brand experiences</p>
                </div>
            </div>
        </div>

        <!-- Team Section -->
        <div class="placeholder">
            <h2>Our Expert Team</h2>
            <div class="team-grid">
                <div class="team-member">
                    <div class="avatar">AS</div>
                    <h4>Ashish Sharma</h4>
                    <p>CEO & Founder</p>
                </div>
                <div class="team-member">
                    <div class="avatar">PG</div>
                    <h4>Priya Gurung</h4>
                    <p>Marketing Director</p>
                </div>
                <div class="team-member">
                    <div class="avatar">RK</div>
                    <h4>Rajesh KC</h4>
                    <p>Technical Lead</p>
                </div>
                <div class="team-member">
                    <div class="avatar">SM</div>
                    <h4>Sita Magar</h4>
                    <p>Creative Director</p>
                </div>
            </div>
        </div>

        <!-- Portfolio Section -->
        <div class="placeholder">
            <h2>Success Stories</h2>
            <div class="portfolio-item">
                <h3>🏔️ Himalayan Coffee House</h3>
                <p>300% increase in social media engagement</p>
                <p>40% increase in foot traffic</p>
            </div>
            <div class="portfolio-item">
                <h3>🎨 Everest Handicrafts</h3>
                <p>500% increase in organic traffic</p>
                <p>200% increase in international orders</p>
            </div>
            <div class="portfolio-item">
                <h3>💻 TechStart Nepal</h3>
                <p>250% increase in qualified leads</p>
                <p>Secured major enterprise clients</p>
            </div>
        </div>

        <!-- Contact Section -->
        <div class="placeholder">
            <h2>Get In Touch</h2>
            <p>📍 Thamel, Kathmandu, Nepal</p>
            <p>📞 +977-1-4441234</p>
            <p>✉️ <EMAIL></p>
            <p>💬 WhatsApp: +977-9841234567</p>
        </div>

        <div class="note">
            <h3>📝 Note for Development</h3>
            <p>This HTML file serves as a visual placeholder generator for the Lunar Cubes website. 
            You can take screenshots of different sections to create placeholder images, or use this 
            as a reference for the actual content structure.</p>
            <p><strong>To create images:</strong> Take screenshots of individual sections and save them 
            as PNG files in the appropriate directories.</p>
        </div>
    </div>

    <script>
        // Add some interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                feature.style.animationDelay = `${index * 0.1}s`;
                feature.style.animation = 'fadeInUp 0.6s ease-out forwards';
            });
        });

        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
