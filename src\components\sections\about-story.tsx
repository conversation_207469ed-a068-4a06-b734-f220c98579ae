'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { Lightbulb, Target, Rocket, Heart } from 'lucide-react';

const storyMilestones = [
  {
    year: '2019',
    title: 'The Beginning',
    description: 'Founded with a vision to bridge the digital gap for Nepali businesses. Started with a small team of passionate digital marketing enthusiasts.',
    icon: Lightbulb,
  },
  {
    year: '2020',
    title: 'First Major Success',
    description: 'Helped our first 50 clients achieve significant digital growth, establishing our reputation in the Kathmandu valley.',
    icon: Target,
  },
  {
    year: '2021',
    title: 'Expansion & Growth',
    description: 'Expanded our services and team, reaching clients across Nepal and achieving 200+ successful projects.',
    icon: Rocket,
  },
  {
    year: '2024',
    title: 'Leading the Market',
    description: 'Now serving 150+ happy clients with a team of 12+ experts, recognized as Nepal\'s premier digital marketing agency.',
    icon: Heart,
  },
];

export default function AboutStory() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="inline-flex items-center px-4 py-2 bg-brand-gold/10 text-brand-navy rounded-full text-sm font-medium mb-6">
              Our Journey
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              From Humble Beginnings to
              <span className="block text-brand-navy">Digital Excellence</span>
            </h2>
            
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              Lunar Cubes was born from a simple observation: Nepali businesses had incredible 
              potential but lacked the digital marketing expertise to reach their full potential 
              in the online world.
            </p>

            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-brand-navy rounded-xl flex items-center justify-center flex-shrink-0">
                  <Lightbulb className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">The Vision</h3>
                  <p className="text-gray-600">
                    To create a digital marketing agency that truly understands the Nepali market, 
                    culture, and business challenges while delivering world-class results.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-brand-gold rounded-xl flex items-center justify-center flex-shrink-0">
                  <Target className="h-6 w-6 text-gray-900" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">The Approach</h3>
                  <p className="text-gray-600">
                    Combining global digital marketing best practices with deep local market 
                    insights to create strategies that actually work for Nepali businesses.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-navy to-brand-gold rounded-xl flex items-center justify-center flex-shrink-0">
                  <Heart className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">The Impact</h3>
                  <p className="text-gray-600">
                    Today, we&apos;ve helped transform hundreds of Nepali businesses, creating jobs, 
                    driving economic growth, and putting Nepal on the digital map.
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Right Content - Timeline */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            {/* Story Image */}
            <div className="relative rounded-2xl overflow-hidden mb-8 shadow-xl">
              <Image
                src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
                alt="Team collaboration and growth"
                width={500}
                height={300}
                className="object-cover w-full h-64"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-brand-navy/60 to-transparent"></div>
              <div className="absolute bottom-4 left-4 text-white">
                <div className="text-sm opacity-90">Our Journey</div>
                <div className="text-lg font-bold">Building Success Together</div>
              </div>
            </div>

            {/* Timeline Line */}
            <div className="absolute left-8 top-80 bottom-0 w-0.5 bg-gradient-to-b from-brand-navy to-brand-gold"></div>

            <div className="space-y-12">
              {storyMilestones.map((milestone, index) => (
                <motion.div
                  key={milestone.year}
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="relative flex items-start space-x-6"
                >
                  {/* Timeline Dot */}
                  <div className="relative z-10 w-16 h-16 bg-white border-4 border-brand-navy rounded-full flex items-center justify-center shadow-lg">
                    <milestone.icon className="h-6 w-6 text-brand-navy" />
                  </div>

                  {/* Content */}
                  <div className="flex-1 bg-gray-50 rounded-2xl p-6">
                    <div className="text-brand-gold font-bold text-lg mb-2">
                      {milestone.year}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">
                      {milestone.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {milestone.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Nepal Focus Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-20"
        >
          <div className="bg-gradient-to-r from-brand-navy to-brand-navy-dark rounded-3xl p-8 md:p-12 text-white">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Why We Focus on Nepal
              </h2>
              <p className="text-blue-100 text-lg mb-8 leading-relaxed">
                Nepal is our home, and we believe in the incredible potential of Nepali businesses. 
                Our deep understanding of local culture, consumer behavior, and market dynamics 
                allows us to create digital marketing strategies that truly resonate.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="text-3xl font-bold text-brand-gold mb-2">100%</div>
                  <div className="text-blue-100">Local Team</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-brand-gold mb-2">5+</div>
                  <div className="text-blue-100">Years in Nepal</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-brand-gold mb-2">150+</div>
                  <div className="text-blue-100">Nepali Businesses Served</div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
