'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { BookOpen, TrendingUp, Users, Award } from 'lucide-react';

const stats = [
  { icon: BookOpen, value: '50+', label: 'Expert Articles' },
  { icon: TrendingUp, value: '10K+', label: 'Monthly Readers' },
  { icon: Users, value: '500+', label: 'Businesses Helped' },
  { icon: Award, value: '5+', label: 'Years Experience' },
];

export default function BlogHero() {
  return (
    <section className="relative py-20 bg-gradient-to-br from-blue-50 via-white to-yellow-50 overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 opacity-5">
        <Image
          src="https://images.unsplash.com/photo-1486312338219-ce68e2c6b7d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80"
          alt="Digital marketing blog background"
          fill
          className="object-cover"
          priority
        />
      </div>
      
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-20 w-64 h-64 bg-brand-gold/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-64 h-64 bg-brand-navy/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center mb-16">
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center px-4 py-2 bg-brand-navy/10 text-brand-navy rounded-full text-sm font-medium mb-6"
          >
            <BookOpen className="w-4 h-4 mr-2 text-brand-gold" />
            Digital Marketing Blog
          </motion.div>

          {/* Main Heading */}
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight"
          >
            Expert Insights for
            <span className="block bg-gradient-to-r from-brand-navy to-brand-gold bg-clip-text text-transparent">
              Digital Success
            </span>
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-lg md:text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed"
          >
            Stay ahead of the curve with our expert digital marketing insights, SEO tips, 
            social media strategies, and growth hacks specifically tailored for Nepali businesses. 
            Learn from real case studies and proven strategies.
          </motion.p>

          {/* Featured Topics */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-wrap justify-center gap-3 mb-12"
          >
            {[
              'Digital Marketing',
              'SEO Tips',
              'Social Media',
              'Content Strategy',
              'Case Studies',
              'Nepal Market'
            ].map((topic, index) => (
              <motion.span
                key={topic}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.7 + index * 0.1 }}
                className="px-4 py-2 bg-white/80 backdrop-blur-sm text-brand-navy rounded-full text-sm font-medium border border-brand-navy/20 hover:bg-brand-navy hover:text-white transition-colors cursor-pointer"
              >
                {topic}
              </motion.span>
            ))}
          </motion.div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-8"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.9 + index * 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-brand-navy to-brand-gold rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="h-8 w-8 text-white" />
                </div>
                <div className="text-2xl md:text-3xl font-bold text-brand-navy mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-600 text-sm">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Search Bar */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.0 }}
          className="max-w-2xl mx-auto"
        >
          <div className="bg-white rounded-2xl p-2 shadow-xl border border-gray-100">
            <div className="flex items-center">
              <input
                type="text"
                placeholder="Search articles, tips, and insights..."
                className="flex-1 px-6 py-4 text-gray-700 placeholder-gray-400 bg-transparent border-none outline-none text-lg"
              />
              <button className="px-8 py-4 bg-gradient-to-r from-brand-navy to-brand-gold text-white rounded-xl font-semibold hover:shadow-lg transition-all duration-300 hover:scale-105">
                Search
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
