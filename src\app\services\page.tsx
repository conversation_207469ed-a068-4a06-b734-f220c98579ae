import { Metadata } from 'next';
import ServicesHero from '@/components/sections/services-hero';
import WebDevelopmentShowcase from '@/components/sections/web-development-showcase';
import ServicesGrid from '@/components/sections/services-grid';
import ServicesCTA from '@/components/sections/services-cta';

export const metadata: Metadata = {
  title: 'Web Development & Digital Marketing Services - Lunar Cubes Nepal',
  description: 'Leading web development and digital marketing agency in Nepal. Custom websites, e-commerce solutions, SEO, social media marketing, and comprehensive digital services for Nepali businesses.',
  keywords: 'web development Nepal, website design Kathmandu, e-commerce development Nepal, digital marketing services Nepal, SEO services Kathmandu, social media marketing Nepal, Google Ads Nepal',
  openGraph: {
    title: 'Web Development & Digital Marketing Services - Lunar Cubes Nepal',
    description: 'Transform your business with cutting-edge web development and expert digital marketing services tailored for Nepali businesses.',
    url: '/services',
  },
};

export default function ServicesPage() {
  return (
    <div>
      <ServicesHero />  
      <WebDevelopmentShowcase />
      <ServicesGrid />
      <ServicesCTA />
    </div>
  );
}
