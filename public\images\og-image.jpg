<!-- This would be a JPEG file in production. For now, creating an SVG placeholder -->
<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="ogGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#ogGradient)"/>
  
  <!-- Logo -->
  <circle cx="200" cy="315" r="80" fill="white" opacity="0.2"/>
  <circle cx="200" cy="315" r="60" fill="white"/>
  <text x="200" y="330" text-anchor="middle" fill="#1e40af" font-family="Arial, sans-serif" font-size="36" font-weight="bold">LC</text>
  
  <!-- Main Text -->
  <text x="350" y="280" fill="white" font-family="Arial, sans-serif" font-size="48" font-weight="bold">Lunar Cubes</text>
  <text x="350" y="330" fill="white" font-family="Arial, sans-serif" font-size="24" opacity="0.9">Digital Marketing Agency Nepal</text>
  <text x="350" y="370" fill="white" font-family="Arial, sans-serif" font-size="20" opacity="0.8">From Concept to Cosmos</text>
  
  <!-- Stats -->
  <g transform="translate(350, 420)">
    <text x="0" y="0" fill="white" font-family="Arial, sans-serif" font-size="16" opacity="0.9">150+ Happy Clients</text>
    <text x="200" y="0" fill="white" font-family="Arial, sans-serif" font-size="16" opacity="0.9">300+ Projects</text>
    <text x="400" y="0" fill="white" font-family="Arial, sans-serif" font-size="16" opacity="0.9">5+ Years Experience</text>
  </g>
  
  <!-- Decorative Elements -->
  <circle cx="1000" cy="150" r="50" fill="white" opacity="0.1"/>
  <circle cx="1100" cy="480" r="30" fill="white" opacity="0.1"/>
  <circle cx="950" cy="500" r="20" fill="white" opacity="0.1"/>
</svg>
