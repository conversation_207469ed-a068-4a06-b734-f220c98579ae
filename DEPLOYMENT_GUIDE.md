# Lunar Cubes Website Deployment Guide

## 🚀 Quick Start

This guide will help you deploy the Lunar Cubes website to production. The website is built with Next.js 15 and can be deployed to various platforms.

## 📋 Pre-Deployment Checklist

### ✅ Code Preparation
- [x] All components built and tested
- [x] TypeScript compilation successful
- [x] No console errors or warnings
- [x] All dependencies installed
- [x] Build process working locally

### ✅ Content Review
- [x] All placeholder content replaced with real content
- [x] Contact information accurate
- [x] Service descriptions finalized
- [x] Team member information correct
- [x] Company details verified

### ✅ SEO Optimization
- [x] Meta tags configured
- [x] robots.txt created
- [x] sitemap.xml generated
- [x] Structured data implemented
- [x] Open Graph tags set

## 🌐 Deployment Options

### Option 1: Vercel (Recommended)

Vercel is the easiest option for Next.js deployment and offers excellent performance.

#### Steps:
1. **Connect Repository**
   ```bash
   # Push your code to GitHub/GitLab/Bitbucket
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   ```

2. **Deploy to Vercel**
   - Visit [vercel.com](https://vercel.com)
   - Sign up/login with your Git provider
   - Import your repository
   - Configure project settings:
     - Framework Preset: Next.js
     - Build Command: `npm run build`
     - Output Directory: `.next`
     - Install Command: `npm install`

3. **Environment Variables**
   ```
   NEXT_PUBLIC_SITE_URL=https://lunarcubes.com.np
   ```

4. **Custom Domain**
   - Add your domain in Vercel dashboard
   - Configure DNS records as instructed
   - SSL certificate will be automatically provisioned

### Option 2: Netlify

#### Steps:
1. **Build Settings**
   - Build command: `npm run build`
   - Publish directory: `out`
   - Node version: 18.x

2. **Add to package.json**
   ```json
   {
     "scripts": {
       "build": "next build && next export"
     }
   }
   ```

3. **Configure next.config.js**
   ```javascript
   /** @type {import('next').NextConfig} */
   const nextConfig = {
     output: 'export',
     trailingSlash: true,
     images: {
       unoptimized: true
     }
   }
   module.exports = nextConfig
   ```

### Option 3: Traditional Web Hosting

For shared hosting or VPS deployment:

1. **Build for Static Export**
   ```bash
   npm run build
   npm run export
   ```

2. **Upload Files**
   - Upload the `out` folder contents to your web server
   - Ensure proper file permissions (644 for files, 755 for directories)

3. **Configure Web Server**
   - Set up proper redirects for SPA routing
   - Configure GZIP compression
   - Set up SSL certificate

## 🔧 Configuration Files

### next.config.js
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: ['lunarcubes.com.np'],
    formats: ['image/webp', 'image/avif'],
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ]
  },
}

module.exports = nextConfig
```

### Environment Variables
Create `.env.local` for local development:
```
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your-ga-id
NEXT_PUBLIC_FACEBOOK_PIXEL_ID=your-pixel-id
```

For production, set these in your hosting platform:
```
NEXT_PUBLIC_SITE_URL=https://lunarcubes.com.np
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your-ga-id
NEXT_PUBLIC_FACEBOOK_PIXEL_ID=your-pixel-id
```

## 📧 Email Configuration

### Contact Form Integration

To make contact forms functional, integrate with an email service:

#### Option 1: EmailJS
```javascript
// Install: npm install @emailjs/browser
import emailjs from '@emailjs/browser';

const sendEmail = async (formData) => {
  try {
    await emailjs.send(
      'your_service_id',
      'your_template_id',
      formData,
      'your_public_key'
    );
  } catch (error) {
    console.error('Email send failed:', error);
  }
};
```

#### Option 2: Formspree
```javascript
// Add to your form
<form action="https://formspree.io/f/your-form-id" method="POST">
  {/* form fields */}
</form>
```

#### Option 3: Netlify Forms (if using Netlify)
```javascript
// Add to your form
<form name="contact" method="POST" data-netlify="true">
  <input type="hidden" name="form-name" value="contact" />
  {/* form fields */}
</form>
```

## 📊 Analytics Setup

### Google Analytics 4
1. Create GA4 property at [analytics.google.com](https://analytics.google.com)
2. Get your Measurement ID (G-XXXXXXXXXX)
3. Add to environment variables
4. Install gtag:
   ```bash
   npm install gtag
   ```

### Facebook Pixel
1. Create pixel at [business.facebook.com](https://business.facebook.com)
2. Get your Pixel ID
3. Add to environment variables

## 🔒 Security Considerations

### SSL Certificate
- Ensure HTTPS is enabled
- Use security headers (already configured in next.config.js)
- Regular security updates

### Content Security Policy
Add to next.config.js:
```javascript
async headers() {
  return [
    {
      source: '/(.*)',
      headers: [
        {
          key: 'Content-Security-Policy',
          value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' *.googletagmanager.com *.google-analytics.com; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; img-src 'self' data: *.google-analytics.com; connect-src 'self' *.google-analytics.com *.analytics.google.com;"
        }
      ]
    }
  ]
}
```

## 🚀 Performance Optimization

### Image Optimization
- Use Next.js Image component
- Optimize images before upload
- Use WebP format when possible

### Caching Strategy
```javascript
// next.config.js
async headers() {
  return [
    {
      source: '/images/(.*)',
      headers: [
        {
          key: 'Cache-Control',
          value: 'public, max-age=31536000, immutable',
        },
      ],
    },
  ]
}
```

## 📱 PWA Configuration (Optional)

To make the site installable as a PWA:

1. Install next-pwa:
   ```bash
   npm install next-pwa
   ```

2. Configure next.config.js:
   ```javascript
   const withPWA = require('next-pwa')({
     dest: 'public'
   })
   
   module.exports = withPWA({
     // your existing config
   })
   ```

## 🔍 Monitoring & Maintenance

### Performance Monitoring
- Set up Google PageSpeed Insights monitoring
- Use Vercel Analytics (if using Vercel)
- Monitor Core Web Vitals

### Error Tracking
Consider integrating:
- Sentry for error tracking
- LogRocket for session replay
- Hotjar for user behavior analysis

### Regular Maintenance
- Update dependencies monthly
- Monitor security vulnerabilities
- Backup content regularly
- Review analytics monthly

## 📞 Support & Troubleshooting

### Common Issues

1. **Build Failures**
   - Check TypeScript errors
   - Verify all dependencies installed
   - Check for syntax errors

2. **Routing Issues**
   - Ensure proper file structure in app directory
   - Check for conflicting routes

3. **Performance Issues**
   - Optimize images
   - Check bundle size
   - Review third-party scripts

### Getting Help
- Next.js Documentation: [nextjs.org/docs](https://nextjs.org/docs)
- Vercel Support: [vercel.com/support](https://vercel.com/support)
- Community: [github.com/vercel/next.js/discussions](https://github.com/vercel/next.js/discussions)

---

**Deployment Status**: ✅ Ready for production deployment
**Last Updated**: January 2024
**Maintained By**: Development Team
