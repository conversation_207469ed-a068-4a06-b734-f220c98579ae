'use client';

import { motion } from 'framer-motion';
import { MapPin, Phone, Mail, Clock, Facebook, Instagram, Linkedin, Twitter, MessageSquare } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useCompanyInfo } from '@/lib/queries/hooks';

export default function ContactInfo() {
  const { data: company } = useCompanyInfo();

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Visit Our Office or
            <span className="block text-brand-navy">Connect Online</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            We&apos;re located in the heart of Kathmandu and always ready to meet in person 
            or connect virtually. Choose what works best for you.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Office Location */}
            <Card className="border-0 shadow-lg">
              <CardContent className="p-8">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-brand-navy to-brand-gold rounded-xl flex items-center justify-center flex-shrink-0">
                    <MapPin className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">Our Office</h3>
                    {company && (
                      <>
                        <p className="text-gray-700 mb-2">
                          {company.location.address}
                        </p>
                        <p className="text-gray-700 mb-4">
                          {company.location.city}, {company.location.country}
                        </p>
                      </>
                    )}
                    <Button 
                      asChild 
                      variant="outline" 
                      size="sm"
                      className="border-brand-navy text-brand-navy hover:bg-brand-navy hover:text-white"
                    >
                      <a 
                        href="https://maps.google.com/?q=Thamel,Kathmandu,Nepal" 
                        target="_blank" 
                        rel="noopener noreferrer"
                      >
                        Get Directions
                      </a>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contact Methods */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Phone */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-brand-navy rounded-lg flex items-center justify-center">
                      <Phone className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Call Us</h4>
                      {company && (
                        <a 
                          href={`tel:${company.contact.phone}`}
                          className="text-brand-navy hover:underline"
                        >
                          {company.contact.phone}
                        </a>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Email */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-brand-gold rounded-lg flex items-center justify-center">
                      <Mail className="h-5 w-5 text-gray-900" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Email Us</h4>
                      {company && (
                        <a 
                          href={`mailto:${company.contact.email}`}
                          className="text-brand-navy hover:underline"
                        >
                          {company.contact.email}
                        </a>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* WhatsApp */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                      <MessageSquare className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">WhatsApp</h4>
                      {company && (
                        <a 
                          href={`https://wa.me/${company.contact.whatsapp?.replace(/[^0-9]/g, '')}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-brand-navy hover:underline"
                        >
                          {company.contact.whatsapp}
                        </a>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Office Hours */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                      <Clock className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Office Hours</h4>
                      <div className="text-sm text-gray-600">
                        <div>Mon-Fri: 9AM-6PM</div>
                        <div>Sat: 10AM-4PM</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Social Media */}
            <Card className="border-0 shadow-lg">
              <CardContent className="p-8">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Follow Us</h3>
                <div className="flex space-x-4">
                  {company?.social && (
                    <>
                      {company.social.facebook && (
                        <a
                          href={company.social.facebook}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center text-white hover:bg-blue-700 transition-colors"
                        >
                          <Facebook className="h-5 w-5" />
                        </a>
                      )}
                      {company.social.instagram && (
                        <a
                          href={company.social.instagram}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-12 h-12 bg-pink-600 rounded-lg flex items-center justify-center text-white hover:bg-pink-700 transition-colors"
                        >
                          <Instagram className="h-5 w-5" />
                        </a>
                      )}
                      {company.social.linkedin && (
                        <a
                          href={company.social.linkedin}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-12 h-12 bg-blue-700 rounded-lg flex items-center justify-center text-white hover:bg-blue-800 transition-colors"
                        >
                          <Linkedin className="h-5 w-5" />
                        </a>
                      )}
                      {company.social.twitter && (
                        <a
                          href={company.social.twitter}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-12 h-12 bg-blue-400 rounded-lg flex items-center justify-center text-white hover:bg-blue-500 transition-colors"
                        >
                          <Twitter className="h-5 w-5" />
                        </a>
                      )}
                    </>
                  )}
                </div>
                <p className="text-gray-600 text-sm mt-4">
                  Stay updated with our latest digital marketing tips, case studies, and industry insights.
                </p>
              </CardContent>
            </Card>
          </motion.div>

          {/* Google Maps */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Card className="border-0 shadow-lg overflow-hidden h-full">
              <CardContent className="p-0 h-full min-h-[600px]">
                <div className="relative w-full h-full">
                  {/* Google Maps Embed */}
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3532.1234567890123!2d85.3240!3d27.7172!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39eb196a8b0b0b0b%3A0x1234567890abcdef!2sThamel%2C%20Kathmandu%2C%20Nepal!5e0!3m2!1sen!2snp!4v1234567890123!5m2!1sen!2snp"
                    width="100%"
                    height="100%"
                    style={{ border: 0, minHeight: '600px' }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title="Lunar Cubes Office Location"
                    className="rounded-lg"
                  ></iframe>
                  
                  {/* Overlay with office info */}
                  <div className="absolute top-4 left-4 bg-white rounded-lg p-4 shadow-lg max-w-xs">
                    <h4 className="font-bold text-gray-900 mb-2">Lunar Cubes Office</h4>
                    <p className="text-sm text-gray-600 mb-2">
                      Thamel, Kathmandu, Nepal
                    </p>
                    <div className="flex items-center text-xs text-gray-500">
                      <Clock className="h-3 w-3 mr-1" />
                      Open Mon-Fri 9AM-6PM
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Emergency Contact */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-brand-navy to-brand-navy-dark rounded-3xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Need Urgent Support?</h3>
            <p className="text-blue-100 mb-6">
              For existing clients with urgent issues, we provide 24/7 emergency support.
            </p>
            <Button 
              asChild 
              size="lg" 
              className="bg-brand-gold hover:bg-brand-gold-dark text-gray-900 font-semibold"
            >
              <a href="tel:+97714441234">
                Call Emergency Line
              </a>
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
