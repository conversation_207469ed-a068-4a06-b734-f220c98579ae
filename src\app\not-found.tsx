import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Home, ArrowLeft, Search } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-yellow-50">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto text-center">
          {/* 404 Number */}
          <div className="text-8xl md:text-9xl font-bold text-brand-navy mb-6 opacity-20">
            404
          </div>
          
          {/* Error Message */}
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Page Not Found
          </h1>
          
          <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto">
            Sorry, we couldn&apos;t find the page you&apos;re looking for. 
            It might have been moved, deleted, or you entered the wrong URL.
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button asChild size="lg" className="bg-brand-navy hover:bg-brand-navy-dark">
              <Link href="/">
                <Home className="mr-2 h-5 w-5" />
                Go Home
              </Link>
            </Button>
            
            <Button asChild variant="outline" size="lg">
              <Link href="/services">
                <Search className="mr-2 h-5 w-5" />
                Browse Services
              </Link>
            </Button>
            
            <Button asChild variant="outline" size="lg">
              <Link href="/contact">
                Contact Us
              </Link>
            </Button>
          </div>

          {/* Helpful Links */}
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Popular Pages
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                { name: 'Digital Marketing Services', href: '/services' },
                { name: 'About Our Agency', href: '/about' },
                { name: 'Our Portfolio', href: '/portfolio' },
                { name: 'Contact Information', href: '/contact' },
                { name: 'Case Studies', href: '/case-studies' },
                { name: 'Free Consultation', href: '/contact' }
              ].map((link) => (
                <Link
                  key={link.name}
                  href={link.href}
                  className="text-brand-navy hover:text-brand-navy-dark hover:underline text-left p-2 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <ArrowLeft className="inline h-4 w-4 mr-2" />
                  {link.name}
                </Link>
              ))}
            </div>
          </div>

          {/* Contact Info */}
          <div className="mt-8 text-sm text-gray-500">
            <p>
              Need help? Contact us at{' '}
              <a 
                href="mailto:<EMAIL>" 
                className="text-brand-navy hover:underline"
              >
                <EMAIL>
              </a>
              {' '}or call{' '}
              <a 
                href="tel:+97714441234" 
                className="text-brand-navy hover:underline"
              >
                +977-1-4441234
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
