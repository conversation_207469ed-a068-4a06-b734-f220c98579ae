import type { Metadata } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";
import QueryProvider from "@/lib/query-provider";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";
import ScrollToTop from "@/components/ui/scroll-to-top";
import WhatsAppFAB from "@/components/ui/whatsapp-fab";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-poppins",
});

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#1e40af',
};

export const metadata: Metadata = {
  metadataBase: new URL('https://lunarcubes.com.np'),
  icons: {
    icon: [
      { url: '/images/logo.svg', type: 'image/svg+xml' },
    ],
    apple: '/apple-touch-icon.png',
  },
  title: "Lunar Cubes - Digital Marketing Agency in Nepal | From Concept to Cosmos",
  description: "Leading digital marketing agency in Nepal helping SMEs grow online. Expert services in social media marketing, SEO, web development, and Google Ads. Serving Kathmandu, Pokhara & beyond.",
  keywords: "digital marketing Nepal, SEO Nepal, social media marketing Kathmandu, web development Nepal, Google Ads Nepal, digital agency Kathmandu",
  authors: [{ name: "Lunar Cubes Digital Marketing" }],
  creator: "Lunar Cubes",
  publisher: "Lunar Cubes",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://lunarcubes.com.np",
    siteName: "Lunar Cubes",
    title: "Lunar Cubes - Digital Marketing Agency in Nepal",
    description: "Leading digital marketing agency in Nepal helping SMEs grow online with expert services in social media marketing, SEO, web development, and Google Ads.",
    images: [
      {
        url: "/images/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Lunar Cubes Digital Marketing Agency Nepal",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    site: "@lunarcubes",
    creator: "@lunarcubes",
  },

};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.variable} ${poppins.variable} font-sans antialiased`}>
        <QueryProvider>
          <div className="flex flex-col min-h-screen">
            <Header />
            <main className="flex-1">
              {children}
            </main>
            <Footer />
          </div>
          <ScrollToTop />
          <WhatsAppFAB />
        </QueryProvider>
      </body>
    </html>
  );
}
