'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRight, Mail, Phone, MapPin, Send } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function CareersCTA() {
  return (
    <section className="py-20 bg-gradient-to-br from-brand-navy via-brand-navy-dark to-gray-900 text-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          {/* Main CTA */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-16"
          >
            <h2 className="text-4xl md:text-6xl font-bold mb-6 font-heading">
              Ready to Start Your{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-brand-gold to-yellow-300">
                Journey?
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Join Nepal&apos;s leading digital marketing agency and be part of something amazing.
              Your next career adventure starts here.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                asChild
                size="lg"
                className="bg-brand-gold hover:bg-yellow-500 text-brand-navy px-8 py-4 text-lg font-semibold group"
              >
                <Link href="#open-positions">
                  Apply Now
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>

              <Button
                asChild
                variant="outline"
                size="lg"
                className="border-white  hover:bg-white text-brand-navy px-8 py-4 text-lg font-semibold"
              >
                <Link href="/contact">
                  Get in Touch
                </Link>
              </Button>
            </div>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16"
          >
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <Mail className="h-8 w-8 text-brand-gold mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Email Us</h3>
              <p className="text-gray-300 text-sm mb-3">Send your resume and cover letter</p>
              <a
                href="mailto:<EMAIL>"
                className="text-brand-gold hover:text-yellow-300 transition-colors font-medium"
              >
                <EMAIL>
              </a>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <Phone className="h-8 w-8 text-brand-gold mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Call Us</h3>
              <p className="text-gray-300 text-sm mb-3">Speak with our HR team</p>
              <a
                href="tel:+9779851234567"
                className="text-brand-gold hover:text-yellow-300 transition-colors font-medium"
              >
                +977 ************
              </a>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <MapPin className="h-8 w-8 text-brand-gold mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Visit Us</h3>
              <p className="text-gray-300 text-sm mb-3">Come for an office tour</p>
              <p className="text-brand-gold font-medium">
                Kathmandu, Nepal
              </p>
            </div>
          </motion.div>

          {/* Application Process */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10"
          >
            <h3 className="text-2xl font-bold mb-8 font-heading">Our Hiring Process</h3>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-brand-gold rounded-full flex items-center justify-center mx-auto mb-4">
                  <Send className="h-6 w-6 text-brand-navy" />
                </div>
                <h4 className="font-semibold mb-2">1. Apply</h4>
                <p className="text-sm text-gray-300">Submit your application and resume</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-brand-gold rounded-full flex items-center justify-center mx-auto mb-4">
                  <Mail className="h-6 w-6 text-brand-navy" />
                </div>
                <h4 className="font-semibold mb-2">2. Review</h4>
                <p className="text-sm text-gray-300">We review your application carefully</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-brand-gold rounded-full flex items-center justify-center mx-auto mb-4">
                  <Phone className="h-6 w-6 text-brand-navy" />
                </div>
                <h4 className="font-semibold mb-2">3. Interview</h4>
                <p className="text-sm text-gray-300">Meet with our team for interviews</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-brand-gold rounded-full flex items-center justify-center mx-auto mb-4">
                  <ArrowRight className="h-6 w-6 text-brand-navy" />
                </div>
                <h4 className="font-semibold mb-2">4. Join</h4>
                <p className="text-sm text-gray-300">Welcome to the Lunar Cubes family!</p>
              </div>
            </div>
          </motion.div>

          {/* Final Message */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
            className="mt-16"
          >
            <p className="text-lg text-gray-300 max-w-2xl mx-auto">
              We&apos;re committed to building a diverse and inclusive team. We encourage applications
              from all qualified candidates regardless of race, gender, age, religion, sexual orientation,
              or disability status.
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
