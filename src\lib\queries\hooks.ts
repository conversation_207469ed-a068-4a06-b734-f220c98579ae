// TanStack Query hooks for data fetching
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  fetchCompanyInfo,
  fetchServices,
  fetchServiceById,
  fetchTeamMembers,
  fetchTeamMemberById,
  fetchTestimonials,
  fetchTestimonialsByService,
  fetchPortfolio,
  fetchPortfolioByCategory,
  fetchPortfolioById,
  fetchPackages,
  fetchPackageById,
  fetchIndustries,
  fetchIndustryById,
  fetchCaseStudies,
  fetchCaseStudyById,
  fetchCaseStudiesByService,
  searchContent,
  submitContactForm,
  subscribeNewsletter,
  fetchJobPostings,
  fetchJobPostingBySlug
} from './api';

// Query keys for consistent caching
export const queryKeys = {
  company: ['company'],
  services: ['services'],
  service: (id: string) => ['service', id],
  team: ['team'],
  teamMember: (id: string) => ['teamMember', id],
  testimonials: ['testimonials'],
  testimonialsByService: (serviceId: string) => ['testimonials', 'service', serviceId],
  portfolio: ['portfolio'],
  portfolioByCategory: (category: string) => ['portfolio', 'category', category],
  portfolioItem: (id: string) => ['portfolio', id],
  packages: ['packages'],
  package: (id: string) => ['package', id],
  industries: ['industries'],
  industry: (id: string) => ['industry', id],
  caseStudies: ['caseStudies'],
  caseStudy: (id: string) => ['caseStudy', id],
  caseStudiesByService: (serviceId: string) => ['caseStudies', 'service', serviceId],
  search: (query: string) => ['search', query],
  jobPostings: ['jobPostings'],
  jobPosting: (slug: string) => ['jobPosting', slug],
};

// Company Information
export const useCompanyInfo = () => {
  return useQuery({
    queryKey: queryKeys.company,
    queryFn: fetchCompanyInfo,
    staleTime: 1000 * 60 * 60, // 1 hour
  });
};

// Services
export const useServices = () => {
  return useQuery({
    queryKey: queryKeys.services,
    queryFn: fetchServices,
    staleTime: 1000 * 60 * 30, // 30 minutes
  });
};

export const useService = (id: string) => {
  return useQuery({
    queryKey: queryKeys.service(id),
    queryFn: () => fetchServiceById(id),
    enabled: !!id,
    staleTime: 1000 * 60 * 30,
  });
};

// Team Members
export const useTeamMembers = () => {
  return useQuery({
    queryKey: queryKeys.team,
    queryFn: fetchTeamMembers,
    staleTime: 1000 * 60 * 60, // 1 hour
  });
};

export const useTeamMember = (id: string) => {
  return useQuery({
    queryKey: queryKeys.teamMember(id),
    queryFn: () => fetchTeamMemberById(id),
    enabled: !!id,
    staleTime: 1000 * 60 * 60,
  });
};

// Testimonials
export const useTestimonials = () => {
  return useQuery({
    queryKey: queryKeys.testimonials,
    queryFn: fetchTestimonials,
    staleTime: 1000 * 60 * 15, // 15 minutes
  });
};

export const useTestimonialsByService = (serviceId: string) => {
  return useQuery({
    queryKey: queryKeys.testimonialsByService(serviceId),
    queryFn: () => fetchTestimonialsByService(serviceId),
    enabled: !!serviceId,
    staleTime: 1000 * 60 * 15,
  });
};

// Portfolio
export const usePortfolio = () => {
  return useQuery({
    queryKey: queryKeys.portfolio,
    queryFn: fetchPortfolio,
    staleTime: 1000 * 60 * 30,
  });
};

export const usePortfolioByCategory = (category: string) => {
  return useQuery({
    queryKey: queryKeys.portfolioByCategory(category),
    queryFn: () => fetchPortfolioByCategory(category),
    enabled: !!category,
    staleTime: 1000 * 60 * 30,
  });
};

export const usePortfolioItem = (id: string) => {
  return useQuery({
    queryKey: queryKeys.portfolioItem(id),
    queryFn: () => fetchPortfolioById(id),
    enabled: !!id,
    staleTime: 1000 * 60 * 30,
  });
};

// Packages
export const usePackages = () => {
  return useQuery({
    queryKey: queryKeys.packages,
    queryFn: fetchPackages,
    staleTime: 1000 * 60 * 30,
  });
};

export const usePackage = (id: string) => {
  return useQuery({
    queryKey: queryKeys.package(id),
    queryFn: () => fetchPackageById(id),
    enabled: !!id,
    staleTime: 1000 * 60 * 30,
  });
};

// Industries
export const useIndustries = () => {
  return useQuery({
    queryKey: queryKeys.industries,
    queryFn: fetchIndustries,
    staleTime: 1000 * 60 * 60,
  });
};

export const useIndustry = (id: string) => {
  return useQuery({
    queryKey: queryKeys.industry(id),
    queryFn: () => fetchIndustryById(id),
    enabled: !!id,
    staleTime: 1000 * 60 * 60,
  });
};

// Case Studies
export const useCaseStudies = () => {
  return useQuery({
    queryKey: queryKeys.caseStudies,
    queryFn: fetchCaseStudies,
    staleTime: 1000 * 60 * 30,
  });
};

export const useCaseStudy = (id: string) => {
  return useQuery({
    queryKey: queryKeys.caseStudy(id),
    queryFn: () => fetchCaseStudyById(id),
    enabled: !!id,
    staleTime: 1000 * 60 * 30,
  });
};

export const useCaseStudiesByService = (serviceId: string) => {
  return useQuery({
    queryKey: queryKeys.caseStudiesByService(serviceId),
    queryFn: () => fetchCaseStudiesByService(serviceId),
    enabled: !!serviceId,
    staleTime: 1000 * 60 * 30,
  });
};

// Search
export const useSearch = (query: string) => {
  return useQuery({
    queryKey: queryKeys.search(query),
    queryFn: () => searchContent(query),
    enabled: !!query && query.length > 2,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Mutations
export const useContactForm = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: submitContactForm,
    onSuccess: () => {
      // Optionally invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['contacts'] });
    },
  });
};

export const useNewsletterSubscription = () => {
  return useMutation({
    mutationFn: subscribeNewsletter,
  });
};

// Job Postings
export const useJobPostings = () => {
  return useQuery({
    queryKey: queryKeys.jobPostings,
    queryFn: fetchJobPostings,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useJobPosting = (slug: string) => {
  return useQuery({
    queryKey: queryKeys.jobPosting(slug),
    queryFn: () => fetchJobPostingBySlug(slug),
    enabled: !!slug,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
