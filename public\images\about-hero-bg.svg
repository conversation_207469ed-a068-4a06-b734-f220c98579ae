<svg width="1200" height="600" viewBox="0 0 1200 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="aboutGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#eff6ff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fefce8;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="aboutCircle1" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:0.15" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:0" />
    </radialGradient>
    <radialGradient id="aboutCircle2" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:0.15" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="600" fill="url(#aboutGradient)"/>
  
  <!-- Decorative Circles -->
  <circle cx="150" cy="100" r="120" fill="url(#aboutCircle1)"/>
  <circle cx="1050" cy="500" r="140" fill="url(#aboutCircle2)"/>
  <circle cx="600" cy="200" r="100" fill="url(#aboutCircle1)" opacity="0.6"/>
  
  <!-- Team/People Icons -->
  <g transform="translate(200, 150)">
    <circle cx="0" cy="0" r="30" fill="#1e40af" opacity="0.1"/>
    <circle cx="0" cy="-8" r="8" fill="#1e40af"/>
    <path d="M-12 8 Q0 15 12 8 Q12 20 0 20 Q-12 20 -12 8" fill="#1e40af" opacity="0.7"/>
  </g>
  
  <!-- Growth Chart Icon -->
  <g transform="translate(1000, 120)">
    <circle cx="0" cy="0" r="30" fill="#f59e0b" opacity="0.1"/>
    <rect x="-15" y="5" width="6" height="10" fill="#f59e0b"/>
    <rect x="-5" y="0" width="6" height="15" fill="#f59e0b"/>
    <rect x="5" y="-8" width="6" height="23" fill="#f59e0b"/>
    <path d="M-10 -5 L10 -15" stroke="#f59e0b" stroke-width="2" marker-end="url(#arrow)"/>
  </g>
  
  <!-- Mission/Target Icon -->
  <g transform="translate(300, 450)">
    <circle cx="0" cy="0" r="25" fill="#1e40af" opacity="0.1"/>
    <circle cx="0" cy="0" r="12" stroke="#1e40af" stroke-width="2" fill="none"/>
    <circle cx="0" cy="0" r="6" stroke="#1e40af" stroke-width="2" fill="none"/>
    <circle cx="0" cy="0" r="2" fill="#1e40af"/>
  </g>
  
  <!-- Innovation/Lightbulb Icon -->
  <g transform="translate(900, 400)">
    <circle cx="0" cy="0" r="25" fill="#f59e0b" opacity="0.1"/>
    <circle cx="0" cy="-5" r="8" stroke="#f59e0b" stroke-width="2" fill="none"/>
    <rect x="-3" y="3" width="6" height="4" fill="#f59e0b"/>
    <path d="M-2 7 L2 7" stroke="#f59e0b" stroke-width="1"/>
  </g>
  
  <!-- Connecting Lines -->
  <path d="M250 200 Q500 150 750 200 Q900 250 1000 150" stroke="#1e40af" stroke-width="1" opacity="0.3" fill="none"/>
  <path d="M350 400 Q600 380 850 420" stroke="#f59e0b" stroke-width="1" opacity="0.3" fill="none"/>
  
  <!-- Floating Elements -->
  <circle cx="80" cy="250" r="3" fill="#1e40af" opacity="0.5"/>
  <circle cx="1120" cy="200" r="2" fill="#f59e0b" opacity="0.5"/>
  <circle cx="450" cy="80" r="2" fill="#1e40af" opacity="0.5"/>
  <circle cx="750" cy="520" r="3" fill="#f59e0b" opacity="0.5"/>
  
  <!-- Arrow marker definition -->
  <defs>
    <marker id="arrow" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L0,6 L9,3 z" fill="#f59e0b"/>
    </marker>
  </defs>
</svg>
