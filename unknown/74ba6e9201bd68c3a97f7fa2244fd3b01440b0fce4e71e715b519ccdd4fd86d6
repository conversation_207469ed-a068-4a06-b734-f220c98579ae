'use client';

import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, Target, Award, Heart } from 'lucide-react';

interface JobPosting {
  id: string;
  slug: string;
  title: string;
  department: string;
  location: string;
  type: 'full-time' | 'part-time' | 'contract' | 'internship';
  status: 'active' | 'closed' | 'draft';
  publishedAt: string;
  updatedAt?: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
  salary?: {
    min: number;
    max: number;
    currency: string;
    period: string;
  };
  duration?: string;
}

interface JobPostingContentProps {
  job: JobPosting;
}

export default function JobPostingContent({ job }: JobPostingContentProps) {
  return (
    <div className="space-y-12">
      {/* Job Description */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="bg-white rounded-2xl p-8 shadow-lg"
      >
        <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
          <Target className="h-6 w-6 text-brand-navy" />
          About This Role
        </h2>
        <div className="prose prose-lg max-w-none">
          <p className="text-gray-600 leading-relaxed">
            {job.description}
          </p>
        </div>
      </motion.section>

      {/* Key Responsibilities */}
      {job.responsibilities && job.responsibilities.length > 0 && (
        <motion.section
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          viewport={{ once: true }}
          className="bg-white rounded-2xl p-8 shadow-lg"
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
            <CheckCircle className="h-6 w-6 text-brand-navy" />
            Key Responsibilities
          </h2>
          <ul className="space-y-4">
            {job.responsibilities.map((responsibility, index) => (
              <motion.li
                key={index}
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-start gap-4"
              >
                <span className="w-2 h-2 bg-brand-gold rounded-full mt-3 flex-shrink-0"></span>
                <span className="text-gray-600 leading-relaxed">{responsibility}</span>
              </motion.li>
            ))}
          </ul>
        </motion.section>
      )}

      {/* Requirements */}
      {job.requirements && job.requirements.length > 0 && (
        <motion.section
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="bg-white rounded-2xl p-8 shadow-lg"
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
            <Award className="h-6 w-6 text-brand-navy" />
            Requirements & Qualifications
          </h2>
          <ul className="space-y-4">
            {job.requirements.map((requirement, index) => (
              <motion.li
                key={index}
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-start gap-4"
              >
                <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-600 leading-relaxed">{requirement}</span>
              </motion.li>
            ))}
          </ul>
        </motion.section>
      )}

      {/* Benefits */}
      {job.benefits && job.benefits.length > 0 && (
        <motion.section
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="bg-gradient-to-br from-brand-navy to-brand-navy-dark rounded-2xl p-8 text-white shadow-lg"
        >
          <h2 className="text-2xl font-bold mb-6 flex items-center gap-3">
            <Heart className="h-6 w-6 text-brand-gold" />
            What We Offer
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {job.benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-start gap-3"
              >
                <span className="w-2 h-2 bg-brand-gold rounded-full mt-3 flex-shrink-0"></span>
                <span className="text-gray-200 leading-relaxed">{benefit}</span>
              </motion.div>
            ))}
          </div>
        </motion.section>
      )}

      {/* Additional Information */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        viewport={{ once: true }}
        className="bg-gray-50 rounded-2xl p-8"
      >
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Additional Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {job.duration && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Duration</h3>
              <p className="text-gray-600">{job.duration}</p>
            </div>
          )}
          
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">Application Deadline</h3>
            <p className="text-gray-600">Open until filled</p>
          </div>
          
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">Start Date</h3>
            <p className="text-gray-600">Immediate or as per mutual agreement</p>
          </div>
          
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">Reports To</h3>
            <p className="text-gray-600">{job.department} Manager</p>
          </div>
        </div>
      </motion.section>

      {/* Equal Opportunity Statement */}
      <motion.section
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.5 }}
        viewport={{ once: true }}
        className="bg-white rounded-2xl p-8 shadow-lg border-l-4 border-brand-gold"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Equal Opportunity Employer</h3>
        <p className="text-gray-600 leading-relaxed">
          Lunar Cubes is an equal opportunity employer committed to building a diverse and inclusive team. 
          We encourage applications from all qualified candidates regardless of race, gender, age, religion, 
          sexual orientation, or disability status. We believe that diversity drives innovation and helps us 
          better serve our clients and community.
        </p>
      </motion.section>
    </div>
  );
}
