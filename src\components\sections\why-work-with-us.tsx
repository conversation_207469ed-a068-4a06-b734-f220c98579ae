'use client';

import { motion } from 'framer-motion';
import {
  Rocket,
  Users,
  TrendingUp,
  Award,
  Coffee,
  Lightbulb,
  Heart,
  Globe
} from 'lucide-react';

const reasons = [
  {
    icon: Rocket,
    title: 'Innovation First',
    description: 'Work with cutting-edge technologies and be part of innovative projects that shape the digital landscape in Nepal.',
    color: 'from-blue-500 to-purple-600'
  },
  {
    icon: TrendingUp,
    title: 'Career Growth',
    description: 'Accelerate your career with mentorship, training programs, and opportunities to lead exciting projects.',
    color: 'from-green-500 to-teal-600'
  },
  {
    icon: Users,
    title: 'Amazing Team',
    description: 'Join a diverse, talented team of professionals who support each other and celebrate successes together.',
    color: 'from-orange-500 to-red-600'
  },
  {
    icon: Coffee,
    title: 'Work-Life Balance',
    description: 'Enjoy flexible working hours, remote work options, and a culture that values your personal time.',
    color: 'from-purple-500 to-pink-600'
  },
  {
    icon: Award,
    title: 'Recognition & Rewards',
    description: 'Your hard work is recognized and rewarded with competitive compensation and performance bonuses.',
    color: 'from-yellow-500 to-orange-600'
  },
  {
    icon: Lightbulb,
    title: 'Learning Culture',
    description: 'Continuous learning opportunities, workshops, conferences, and skill development programs.',
    color: 'from-indigo-500 to-blue-600'
  },
  {
    icon: Heart,
    title: 'Meaningful Work',
    description: 'Make a real impact by helping Nepali businesses grow and succeed in the digital world.',
    color: 'from-pink-500 to-rose-600'
  },
  {
    icon: Globe,
    title: 'Global Exposure',
    description: 'Work on international projects and gain exposure to global best practices and standards.',
    color: 'from-teal-500 to-cyan-600'
  }
];

export default function WhyWorkWithUs() {
  return (
    <section id="company-culture" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 font-heading">
            Why Choose{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-brand-navy to-brand-gold">
              Lunar Cubes?
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We believe that great work comes from great people in a great environment.
            Here&apos;s what makes Lunar Cubes an exceptional place to build your career.
          </p>
        </motion.div>

        {/* Reasons Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {reasons.map((reason, index) => (
            <motion.div
              key={reason.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:-translate-y-2 h-full">
                {/* Icon */}
                <div className={`w-16 h-16 rounded-xl bg-gradient-to-r ${reason.color} p-4 mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <reason.icon className="w-full h-full text-white" />
                </div>

                {/* Content */}
                <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-brand-navy transition-colors">
                  {reason.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {reason.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Company Values */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-20 text-center"
        >
          <div className="bg-gradient-to-r from-brand-navy to-brand-navy-dark rounded-3xl p-12 text-white">
            <h3 className="text-3xl font-bold mb-6 font-heading">Our Core Values</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <h4 className="text-xl font-semibold mb-3 text-brand-gold">Excellence</h4>
                <p className="text-gray-300">We strive for excellence in everything we do, from client work to team collaboration.</p>
              </div>
              <div>
                <h4 className="text-xl font-semibold mb-3 text-brand-gold">Innovation</h4>
                <p className="text-gray-300">We embrace new technologies and creative solutions to stay ahead of the curve.</p>
              </div>
              <div>
                <h4 className="text-xl font-semibold mb-3 text-brand-gold">Integrity</h4>
                <p className="text-gray-300">We build trust through transparency, honesty, and ethical business practices.</p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
