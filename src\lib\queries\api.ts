// API functions for fetching data from mock JSON files
import {
  Service,
  TeamMember,
  Testimonial,
  Portfolio,
  Package,
  Industry,
  CaseStudy,
  CompanyInfo,
  ContactForm,
  JobPosting,
} from "@/types";

// Base API URL - in production this would be your actual API endpoint
// const API_BASE = '/api'; // Currently unused but kept for future API implementation

// Simulate API delay for realistic loading states
const simulateDelay = (ms: number = 500) =>
  new Promise((resolve) => setTimeout(resolve, ms));

// Generic fetch function with error handling
async function fetchData<T>(endpoint: string): Promise<T> {
  await simulateDelay();

  try {
    const response = await fetch(endpoint);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error fetching ${endpoint}:`, error);
    throw new Error(`Failed to fetch data from ${endpoint}`);
  }
}

// Company Information
export const fetchCompanyInfo = async (): Promise<CompanyInfo> => {
  return fetchData<CompanyInfo>("/data/company.json");
};

// Services
export const fetchServices = async (): Promise<Service[]> => {
  return fetchData<Service[]>("/data/services.json");
};

export const fetchServiceById = async (id: string): Promise<Service | null> => {
  const services = await fetchServices();
  return services.find((service) => service.id === id) || null;
};

// Team Members
export const fetchTeamMembers = async (): Promise<TeamMember[]> => {
  return fetchData<TeamMember[]>("/data/team.json");
};

export const fetchTeamMemberById = async (
  id: string
): Promise<TeamMember | null> => {
  const team = await fetchTeamMembers();
  return team.find((member) => member.id === id) || null;
};

// Testimonials
export const fetchTestimonials = async (): Promise<Testimonial[]> => {
  return fetchData<Testimonial[]>("/data/testimonials.json");
};

export const fetchTestimonialsByService = async (
  serviceId: string
): Promise<Testimonial[]> => {
  const testimonials = await fetchTestimonials();
  return testimonials.filter((testimonial) =>
    testimonial.serviceUsed.toLowerCase().includes(serviceId.toLowerCase())
  );
};

// Portfolio
export const fetchPortfolio = async (): Promise<Portfolio[]> => {
  return fetchData<Portfolio[]>("/data/portfolio.json");
};

export const fetchPortfolioByCategory = async (
  category: string
): Promise<Portfolio[]> => {
  const portfolio = await fetchPortfolio();
  return portfolio.filter((item) =>
    item.category.toLowerCase().includes(category.toLowerCase())
  );
};

export const fetchPortfolioById = async (
  id: string
): Promise<Portfolio | null> => {
  const portfolio = await fetchPortfolio();
  return portfolio.find((item) => item.id === id) || null;
};

// Packages
export const fetchPackages = async (): Promise<Package[]> => {
  return fetchData<Package[]>("/data/packages.json");
};

export const fetchPackageById = async (id: string): Promise<Package | null> => {
  const packages = await fetchPackages();
  return packages.find((pkg) => pkg.id === id) || null;
};

// Industries
export const fetchIndustries = async (): Promise<Industry[]> => {
  return fetchData<Industry[]>("/data/industries.json");
};

export const fetchIndustryById = async (
  id: string
): Promise<Industry | null> => {
  const industries = await fetchIndustries();
  return industries.find((industry) => industry.id === id) || null;
};

// Case Studies
export const fetchCaseStudies = async (): Promise<CaseStudy[]> => {
  return fetchData<CaseStudy[]>("/data/case-studies.json");
};

export const fetchCaseStudyById = async (
  id: string
): Promise<CaseStudy | null> => {
  const caseStudies = await fetchCaseStudies();
  return caseStudies.find((study) => study.id === id) || null;
};

export const fetchCaseStudiesByService = async (
  serviceId: string
): Promise<CaseStudy[]> => {
  const caseStudies = await fetchCaseStudies();
  return caseStudies.filter((study) => study.serviceUsed === serviceId);
};

// Search functionality
export const searchContent = async (
  query: string
): Promise<{
  services: Service[];
  portfolio: Portfolio[];
  caseStudies: CaseStudy[];
}> => {
  const [services, portfolio, caseStudies] = await Promise.all([
    fetchServices(),
    fetchPortfolio(),
    fetchCaseStudies(),
  ]);

  const searchTerm = query.toLowerCase();

  return {
    services: services.filter(
      (service) =>
        service.title.toLowerCase().includes(searchTerm) ||
        service.description.toLowerCase().includes(searchTerm) ||
        service.features.some((feature) =>
          feature.toLowerCase().includes(searchTerm)
        )
    ),
    portfolio: portfolio.filter(
      (item) =>
        item.title.toLowerCase().includes(searchTerm) ||
        item.description.toLowerCase().includes(searchTerm) ||
        item.category.toLowerCase().includes(searchTerm)
    ),
    caseStudies: caseStudies.filter(
      (study) =>
        study.title.toLowerCase().includes(searchTerm) ||
        study.challenge.toLowerCase().includes(searchTerm) ||
        study.solution.toLowerCase().includes(searchTerm)
    ),
  };
};

// Contact form submission (mock)
export const submitContactForm = async (
  formData: ContactForm
): Promise<{ success: boolean; message: string }> => {
  await simulateDelay(1000);

  // Simulate form validation
  if (!formData.name || !formData.email || !formData.message) {
    throw new Error("Please fill in all required fields");
  }

  // Simulate success response
  return {
    success: true,
    message:
      "Thank you for your message! We will get back to you within 24 hours.",
  };
};

// Newsletter subscription (mock)
export const subscribeNewsletter = async (
  email: string
): Promise<{ success: boolean; message: string }> => {
  await simulateDelay(800);

  if (!email || !email.includes("@")) {
    throw new Error("Please enter a valid email address");
  }

  return {
    success: true,
    message: "Successfully subscribed to our newsletter!",
  };
};

// Job Postings
export const fetchJobPostings = async (): Promise<JobPosting[]> => {
  return fetchData<JobPosting[]>("/data/jobs.json");
};

export const fetchJobPostingBySlug = async (
  slug: string
): Promise<JobPosting | null> => {
  const jobs = await fetchJobPostings();
  return (
    jobs.find((job) => job.slug === slug && job.status === "active") || null
  );
};
