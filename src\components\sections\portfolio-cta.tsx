'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { <PERSON>R<PERSON>, CheckCircle, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';

const benefits = [
  'Free strategy consultation',
  'Custom proposal within 24 hours',
  'Proven track record of success',
  'Local market expertise',
  'Transparent pricing',
  'Dedicated account management'
];

export default function PortfolioCTA() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Main CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="bg-gradient-to-r from-brand-navy to-brand-navy-dark rounded-3xl p-8 md:p-12 text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Be Our Next
              <span className="block text-brand-gold">Success Story?</span>
            </h2>
            <p className="text-blue-100 text-lg mb-8 max-w-2xl mx-auto">
              Join the ranks of successful Nepali businesses who have transformed their 
              digital presence with our proven strategies. Let&apos;s create your success story together.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button 
                asChild 
                size="lg" 
                className="bg-brand-gold hover:bg-brand-gold-dark text-gray-900 font-semibold px-8 py-4"
              >
                <Link href="/contact">
                  Start Your Project
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button 
                asChild 
                variant="outline" 
                size="lg" 
                className="border-white hover:bg-white text-brand-navy px-8 py-4"
              >
                <Link href="/services">
                  Explore Services
                </Link>
              </Button>
            </div>

            {/* Benefits Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={benefit}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-center space-x-2 text-sm text-blue-100"
                >
                  <CheckCircle className="h-4 w-4 text-brand-gold flex-shrink-0" />
                  <span>{benefit}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Client Testimonial */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-white rounded-3xl p-8 md:p-12 shadow-lg mb-16"
        >
          <div className="text-center mb-8">
            <div className="flex justify-center space-x-1 mb-4">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="h-6 w-6 text-brand-gold fill-current" />
              ))}
            </div>
            <blockquote className="text-xl md:text-2xl text-gray-700 italic mb-6">
              &quot;Working with Lunar Cubes was the best decision for our business. 
              They helped us reach international customers and our online sales have tripled!&quot;
            </blockquote>
            <div className="text-brand-navy font-semibold">
              Sunita Pradhan, Managing Director
            </div>
            <div className="text-gray-600">
              Everest Handicrafts
            </div>
          </div>
        </motion.div>

        {/* Process Overview */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-12">
            How We Create Success Stories
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              {
                step: '01',
                title: 'Discovery',
                description: 'We analyze your business, goals, and target audience to understand your unique needs.'
              },
              {
                step: '02',
                title: 'Strategy',
                description: 'We develop a customized digital marketing strategy tailored to your specific objectives.'
              },
              {
                step: '03',
                title: 'Execution',
                description: 'Our expert team implements the strategy with precision and attention to detail.'
              },
              {
                step: '04',
                title: 'Results',
                description: 'We monitor, optimize, and deliver measurable results that drive your business growth.'
              }
            ].map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-brand-navy to-brand-gold rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-lg">{step.step}</span>
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-3">{step.title}</h4>
                <p className="text-gray-600 leading-relaxed">{step.description}</p>
              </motion.div>
            ))}
          </div>

          <div className="mt-12">
            <Button asChild size="lg" className="bg-brand-navy hover:bg-brand-navy-dark">
              <Link href="/contact">
                Get Started Today
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
