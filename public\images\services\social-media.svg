<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="socialGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="90" fill="url(#socialGradient)" opacity="0.1"/>
  <circle cx="100" cy="100" r="70" fill="url(#socialGradient)" opacity="0.2"/>
  
  <!-- Main Icon -->
  <g transform="translate(100, 100)">
    <!-- Phone/Device -->
    <rect x="-25" y="-35" width="50" height="70" rx="8" fill="url(#socialGradient)" stroke="white" stroke-width="2"/>
    
    <!-- Screen -->
    <rect x="-20" y="-25" width="40" height="50" rx="4" fill="white"/>
    
    <!-- Social Media Elements -->
    <circle cx="-10" cy="-15" r="4" fill="#1e40af"/>
    <circle cx="10" cy="-15" r="4" fill="#f59e0b"/>
    <rect x="-15" y="-5" width="30" height="2" fill="#1e40af" opacity="0.6"/>
    <rect x="-15" y="0" width="20" height="2" fill="#1e40af" opacity="0.4"/>
    <rect x="-15" y="5" width="25" height="2" fill="#1e40af" opacity="0.6"/>
    
    <!-- Interaction Icons -->
    <circle cx="-8" cy="15" r="3" fill="#f59e0b"/>
    <path d="M-2 12 L2 15 L-2 18" stroke="#f59e0b" stroke-width="1.5" fill="none"/>
    <path d="M8 12 L12 15 L8 18" stroke="#1e40af" stroke-width="1.5" fill="none"/>
  </g>
  
  <!-- Floating Elements -->
  <circle cx="50" cy="60" r="3" fill="#f59e0b" opacity="0.6"/>
  <circle cx="150" cy="60" r="2" fill="#1e40af" opacity="0.6"/>
  <circle cx="50" cy="140" r="2" fill="#1e40af" opacity="0.6"/>
  <circle cx="150" cy="140" r="3" fill="#f59e0b" opacity="0.6"/>
  
  <!-- Connection Lines -->
  <path d="M70 80 Q100 60 130 80" stroke="#1e40af" stroke-width="1" opacity="0.3" fill="none"/>
  <path d="M70 120 Q100 140 130 120" stroke="#f59e0b" stroke-width="1" opacity="0.3" fill="none"/>
</svg>
