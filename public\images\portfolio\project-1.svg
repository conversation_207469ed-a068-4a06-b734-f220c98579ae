<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="projectGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#projectGradient1)"/>
  
  <!-- Overlay -->
  <rect width="400" height="300" fill="black" opacity="0.2"/>
  
  <!-- Project Icon/Logo -->
  <circle cx="200" cy="120" r="40" fill="white" opacity="0.2"/>
  <circle cx="200" cy="120" r="30" fill="white"/>
  <text x="200" y="130" text-anchor="middle" fill="#1e40af" font-family="Arial, sans-serif" font-size="20" font-weight="bold">🏔️</text>
  
  <!-- Project Title -->
  <text x="200" y="180" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">Himalayan Coffee</text>
  <text x="200" y="205" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" opacity="0.9">Social Media Marketing</text>
  
  <!-- Results -->
  <g transform="translate(50, 230)">
    <text x="0" y="0" fill="#f59e0b" font-family="Arial, sans-serif" font-size="14" font-weight="bold">+300% Engagement</text>
    <text x="150" y="0" fill="#f59e0b" font-family="Arial, sans-serif" font-size="14" font-weight="bold">+40% Traffic</text>
  </g>
  
  <!-- Decorative Elements -->
  <circle cx="50" cy="50" r="3" fill="white" opacity="0.4"/>
  <circle cx="350" cy="50" r="2" fill="white" opacity="0.4"/>
  <circle cx="50" cy="250" r="2" fill="white" opacity="0.4"/>
  <circle cx="350" cy="250" r="3" fill="white" opacity="0.4"/>
</svg>
