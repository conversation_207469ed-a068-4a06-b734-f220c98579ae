import { <PERSON>ada<PERSON> } from 'next';
import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeft, ExternalLink, Calendar, Users, TrendingUp, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Portfolio } from '@/types';

// Server component for better SEO
async function getPortfolioItem(id: string): Promise<Portfolio | null> {
  try {
    // Always use file system during build time or development
    if (process.env.NODE_ENV === 'development' || !process.env.NEXT_PUBLIC_BASE_URL) {
      const fs = await import('fs');
      const path = await import('path');
      const filePath = path.join(process.cwd(), 'public/data/portfolio.json');
      const fileContents = fs.readFileSync(filePath, 'utf8');
      const portfolio: Portfolio[] = JSON.parse(fileContents);
      return portfolio.find((item: Portfolio) => item.id === id) || null;
    }

    // In production with base URL, fetch from URL
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/data/portfolio.json`, {
      cache: 'force-cache',
    });
    if (!response.ok) throw new Error('Failed to fetch portfolio');
    const portfolio: Portfolio[] = await response.json();
    return portfolio.find((item: Portfolio) => item.id === id) || null;
  } catch (error) {
    console.error('Error fetching portfolio item:', error);
    return null;
  }
}

// Generate static params for all portfolio items
export async function generateStaticParams() {
  try {
    // Always use file system during build time or development
    if (process.env.NODE_ENV === 'development' || !process.env.NEXT_PUBLIC_BASE_URL) {
      const fs = await import('fs');
      const path = await import('path');
      const filePath = path.join(process.cwd(), 'public/data/portfolio.json');
      const fileContents = fs.readFileSync(filePath, 'utf8');
      const portfolio: Portfolio[] = JSON.parse(fileContents);
      return portfolio.map((item: Portfolio) => ({
        id: item.id,
      }));
    }

    // In production with base URL, fetch from URL
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/data/portfolio.json`, {
      cache: 'force-cache',
    });
    if (!response.ok) throw new Error('Failed to fetch portfolio');
    const portfolio: Portfolio[] = await response.json();
    return portfolio.map((item: Portfolio) => ({
      id: item.id,
    }));
  } catch (error) {
    console.error('Error generating static params:', error);
    return [];
  }
}

// Generate metadata for each portfolio item
export async function generateMetadata({ params }: { params: Promise<{ id: string }> }): Promise<Metadata> {
  const resolvedParams = await params;
  const item = await getPortfolioItem(resolvedParams.id);

  if (!item) {
    return {
      title: 'Portfolio Item Not Found | Lunar Cubes',
      description: 'The requested portfolio item could not be found.',
    };
  }

  return {
    title: `${item.title} - Portfolio | Lunar Cubes`,
    description: item.description,
    keywords: `${item.category}, ${item.client}, digital marketing portfolio, case study, ${item.technologies?.join(', ')}`,
    openGraph: {
      title: `${item.title} - Portfolio Case Study`,
      description: item.description,
      images: [item.image],
      url: `/portfolio/${item.id}`,
    },
  };
}

interface PortfolioItemPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function PortfolioItemPage({ params }: PortfolioItemPageProps) {
  const resolvedParams = await params;
  const item = await getPortfolioItem(resolvedParams.id);

  if (!item) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-96 overflow-hidden">
        <Image
          src={item.image}
          alt={`${item.client} - ${item.category} project`}
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
        
        {/* Back Button */}
        <div className="absolute top-8 left-8">
          <Button variant="outline" size="sm" className="bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white hover:text-gray-900" asChild>
            <Link href="/portfolio">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Portfolio
            </Link>
          </Button>
        </div>

        {/* Hero Content */}
        <div className="absolute bottom-8 left-8 right-8">
          <div className="container mx-auto">
            <Badge className="bg-brand-gold text-gray-900 mb-4">
              {item.category}
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              {item.title}
            </h1>
            <p className="text-xl text-gray-200 max-w-3xl">
              {item.description}
            </p>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-3 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* Project Overview */}
              <Card className="mb-8">
                <CardHeader>
                  <CardTitle className="text-2xl text-gray-900">Project Overview</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 leading-relaxed mb-6">
                    {item.description}
                  </p>
                  
                  {/* Technologies Used */}
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Technologies Used</h3>
                    <div className="flex flex-wrap gap-2">
                      {item.technologies.map((tech: string, index: number) => (
                        <Badge key={index} variant="outline" className="border-brand-navy text-brand-navy">
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Project Link */}
                  {item.url && (
                    <div className="flex gap-4">
                      <Button className="bg-brand-navy hover:bg-brand-navy-dark" asChild>
                        <Link href={item.url} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="h-4 w-4 mr-2" />
                          View Live Project
                        </Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Results Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl text-gray-900 flex items-center">
                    <TrendingUp className="h-6 w-6 mr-2 text-brand-gold" />
                    Project Results
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 gap-6">
                    {item.results.map((result, index: number) => (
                      <div key={index} className="bg-gray-50 rounded-xl p-6">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-600">{result.metric}</span>
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        </div>
                        <div className="text-3xl font-bold text-brand-navy mb-1">{result.value}</div>
                        <div className="text-sm text-brand-gold font-medium">{result.improvement}</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              {/* Project Details */}
              <Card className="mb-8">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-900">Project Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-brand-gold mr-3" />
                    <div>
                      <div className="text-sm text-gray-600">Client</div>
                      <div className="font-semibold text-gray-900">{item.client}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-brand-gold mr-3" />
                    <div>
                      <div className="text-sm text-gray-600">Duration</div>
                      <div className="font-semibold text-gray-900">{item.duration}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <Badge className="bg-brand-navy text-white">
                      {item.category}
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              {/* CTA Card */}
              <Card className="bg-gradient-to-br from-brand-navy to-brand-navy-dark text-white">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold mb-3">Ready to Start Your Project?</h3>
                  <p className="text-blue-100 mb-6">
                    Let&apos;s discuss how we can help your business achieve similar results.
                  </p>
                  <Button className="w-full bg-brand-gold text-gray-900 hover:bg-brand-gold/90" asChild>
                    <Link href="/contact">
                      Get Started Today
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
