'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { Phone, Mail, MapPin, Clock, MessageSquare } from 'lucide-react';

const contactMethods = [
  {
    icon: Phone,
    title: 'Call Us',
    value: '+977-1-4441234',
    description: 'Mon-Fr<PERSON>, 9:00 AM - 6:00 PM',
    href: 'tel:+97714441234'
  },
  {
    icon: Mail,
    title: 'Email Us',
    value: '<EMAIL>',
    description: 'We reply within 24 hours',
    href: 'mailto:<EMAIL>'
  },
  {
    icon: MessageSquare,
    title: 'WhatsApp',
    value: '+977-9841234567',
    description: 'Quick chat anytime',
    href: 'https://wa.me/9779841234567'
  },
  {
    icon: MapPin,
    title: 'Visit Us',
    value: 'Thamel, Kathmandu',
    description: 'Nepal',
    href: 'https://maps.google.com/?q=Thamel,Kathmandu,Nepal'
  }
];

export default function ContactHero() {
  return (
    <section className="relative py-20 bg-gradient-to-br from-blue-50 via-white to-yellow-50 overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 opacity-5">
        <Image
          src="https://images.unsplash.com/photo-1423666639041-f56000c27a9a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80"
          alt="Contact and communication background"
          fill
          className="object-cover"
          priority
        />
      </div>

      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-20 w-64 h-64 bg-brand-gold/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-64 h-64 bg-brand-navy/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center mb-16">
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center px-4 py-2 bg-brand-navy/10 text-brand-navy rounded-full text-sm font-medium mb-6"
          >
            <MessageSquare className="w-4 h-4 mr-2 text-brand-gold" />
            Get In Touch
          </motion.div>

          {/* Main Heading */}
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight"
          >
            Let&apos;s Start Your Digital
            <span className="block bg-gradient-to-r from-brand-navy to-brand-gold bg-clip-text text-transparent">
              Success Story
            </span>
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-lg md:text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed"
          >
            Ready to transform your business with digital marketing? We&apos;re here to help you 
            every step of the way. Get in touch for a free consultation and discover how we can 
            help your business thrive online.
          </motion.p>

          {/* Quick Stats */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16"
          >
            {[
              { value: '24hrs', label: 'Response Time' },
              { value: '150+', label: 'Happy Clients' },
              { value: '5+', label: 'Years Experience' },
              { value: '98%', label: 'Satisfaction Rate' }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.7 + index * 0.1 }}
                className="text-center"
              >
                <div className="text-2xl md:text-3xl font-bold text-brand-navy mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-600 text-sm">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Contact Methods Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {contactMethods.map((method, index) => (
            <motion.a
              key={method.title}
              href={method.href}
              target={method.href.startsWith('http') ? '_blank' : undefined}
              rel={method.href.startsWith('http') ? 'noopener noreferrer' : undefined}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
              className="group"
            >
              <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-gray-100 h-full">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-navy to-brand-gold rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <method.icon className="h-6 w-6 text-white" />
                </div>
                
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-brand-navy transition-colors">
                  {method.title}
                </h3>
                
                <p className="text-brand-navy font-medium mb-1">
                  {method.value}
                </p>
                
                <p className="text-gray-600 text-sm">
                  {method.description}
                </p>
              </div>
            </motion.a>
          ))}
        </div>

        {/* Office Hours */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="mt-16 text-center"
        >
          <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100 max-w-2xl mx-auto">
            <div className="flex items-center justify-center mb-4">
              <Clock className="h-8 w-8 text-brand-gold mr-3" />
              <h3 className="text-xl font-bold text-gray-900">Office Hours</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-600">
              <div>
                <div className="font-semibold text-gray-900">Monday - Friday</div>
                <div>9:00 AM - 6:00 PM</div>
              </div>
              <div>
                <div className="font-semibold text-gray-900">Saturday</div>
                <div>10:00 AM - 4:00 PM</div>
              </div>
            </div>
            <div className="mt-4 text-sm text-gray-500">
              Sunday: Closed • Emergency support available 24/7
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
