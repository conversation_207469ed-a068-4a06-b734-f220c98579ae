import { Metadata } from 'next';
import PortfolioHero from '@/components/sections/portfolio-hero';
import PortfolioGrid from '@/components/sections/portfolio-grid';
import PortfolioCTA from '@/components/sections/portfolio-cta';

export const metadata: Metadata = {
  title: 'Portfolio - Lunar Cubes Digital Marketing Success Stories',
  description: 'Explore our portfolio of successful digital marketing campaigns for Nepali businesses. See real results, case studies, and client success stories from our expert team.',
  keywords: 'digital marketing portfolio Nepal, case studies Nepal, client success stories, marketing results Nepal, Lunar Cubes portfolio',
  openGraph: {
    title: 'Portfolio - Digital Marketing Success Stories Nepal',
    description: 'See how we\'ve helped 150+ Nepali businesses achieve remarkable growth through strategic digital marketing campaigns.',
    url: '/portfolio',
  },
};

export default function PortfolioPage() {
  return (
    <div>
      <PortfolioHero />
      <PortfolioGrid />
      <PortfolioCTA />
    </div>
  );
}
