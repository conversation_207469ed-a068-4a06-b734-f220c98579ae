'use client';

import { motion } from 'framer-motion';
import {
  Heart,
  GraduationCap,
  Clock,
  Coffee,
  Gift,
  Plane,
  Shield,
  Trophy,
  Laptop,
  Users,
  Home,
  Zap
} from 'lucide-react';

const benefits = [
  {
    category: 'Health & Wellness',
    icon: Heart,
    color: 'from-red-500 to-pink-600',
    items: [
      'Comprehensive health insurance',
      'Annual health checkups',
      'Mental health support',
      'Gym membership allowance'
    ]
  },
  {
    category: 'Learning & Development',
    icon: GraduationCap,
    color: 'from-blue-500 to-indigo-600',
    items: [
      'Professional development budget',
      'Conference and workshop attendance',
      'Online course subscriptions',
      'Mentorship programs'
    ]
  },
  {
    category: 'Work-Life Balance',
    icon: Clock,
    color: 'from-green-500 to-teal-600',
    items: [
      'Flexible working hours',
      'Remote work options',
      'Paid time off',
      'Festival bonuses'
    ]
  },
  {
    category: 'Office Perks',
    icon: Coffee,
    color: 'from-orange-500 to-yellow-600',
    items: [
      'Free snacks and beverages',
      'Modern office space',
      'Gaming and relaxation area',
      'Team lunch every Friday'
    ]
  },
  {
    category: 'Financial Benefits',
    icon: Gift,
    color: 'from-purple-500 to-pink-600',
    items: [
      'Competitive salary',
      'Performance bonuses',
      'Annual salary reviews',
      'Provident fund'
    ]
  },
  {
    category: 'Travel & Events',
    icon: Plane,
    color: 'from-teal-500 to-cyan-600',
    items: [
      'Annual company retreats',
      'Team building activities',
      'Client visit opportunities',
      'Industry event participation'
    ]
  }
];

const additionalPerks = [
  { icon: Shield, title: 'Job Security', description: 'Stable employment with growth opportunities' },
  { icon: Trophy, title: 'Recognition', description: 'Employee of the month awards and achievements' },
  { icon: Laptop, title: 'Equipment', description: 'Latest laptops and necessary work equipment' },
  { icon: Users, title: 'Team Culture', description: 'Collaborative and supportive work environment' },
  { icon: Home, title: 'Work From Home', description: 'Hybrid work model with flexible arrangements' },
  { icon: Zap, title: 'Innovation Time', description: '20% time for personal projects and innovation' }
];

export default function CompanyBenefits() {
  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 font-heading">
            Amazing{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-brand-navy to-brand-gold">
              Benefits
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We believe in taking care of our team members. Here are some of the benefits
            and perks you&apos;ll enjoy as part of the Lunar Cubes family.
          </p>
        </motion.div>

        {/* Main Benefits Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {benefits.map((benefit, index) => (
            <motion.div
              key={benefit.category}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:-translate-y-2 h-full">
                {/* Icon */}
                <div className={`w-16 h-16 rounded-xl bg-gradient-to-r ${benefit.color} p-4 mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <benefit.icon className="w-full h-full text-white" />
                </div>

                {/* Category Title */}
                <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-brand-navy transition-colors">
                  {benefit.category}
                </h3>

                {/* Benefit Items */}
                <ul className="space-y-3">
                  {benefit.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start gap-3">
                      <span className="w-2 h-2 bg-brand-gold rounded-full mt-2 flex-shrink-0"></span>
                      <span className="text-gray-600">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Additional Perks */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="bg-gradient-to-r from-brand-navy to-brand-navy-dark rounded-3xl p-12 text-white"
        >
          <h3 className="text-3xl font-bold text-center mb-12 font-heading">
            And Many More Perks...
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {additionalPerks.map((perk, index) => (
              <motion.div
                key={perk.title}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-start gap-4"
              >
                <div className="w-12 h-12 bg-brand-gold/20 rounded-lg p-3 flex-shrink-0">
                  <perk.icon className="w-full h-full text-brand-gold" />
                </div>
                <div>
                  <h4 className="font-semibold text-lg mb-2">{perk.title}</h4>
                  <p className="text-gray-300 text-sm">{perk.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-white rounded-3xl p-12 shadow-lg">
            <h3 className="text-3xl font-bold text-gray-900 mb-4 font-heading">
              Ready to Join Our Team?
            </h3>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Don&apos;t see a position that fits? We&apos;re always looking for exceptional talent.
              Send us your resume and let&apos;s start a conversation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.a
                href="#open-positions"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-brand-navy hover:bg-brand-navy-dark text-white px-8 py-4 rounded-lg font-semibold transition-colors"
              >
                View Open Positions
              </motion.a>
              <motion.a
                href="/contact"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-brand-navy text-brand-navy hover:bg-brand-navy hover:text-white px-8 py-4 rounded-lg font-semibold transition-all"
              >
                Send Your Resume
              </motion.a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
