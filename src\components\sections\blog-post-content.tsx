'use client';

import { motion } from 'framer-motion';
import { Share2, Bookmark, ThumbsUp, MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface BlogPost {
  id: string;
  title: string;
  content: string;
  tags: string[];
}

interface BlogPostContentProps {
  post: BlogPost;
}

export default function BlogPostContent({ post }: BlogPostContentProps) {
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: post.title,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    }
  };

  return (
    <motion.article
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
      className="prose prose-lg max-w-none"
    >
      {/* Article Content */}
      <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 mb-8">
        <div className="prose prose-lg prose-brand max-w-none">
          {/* This would normally render markdown content */}
          <div className="text-gray-700 leading-relaxed space-y-6">
            <p className="text-xl leading-relaxed text-gray-800 font-medium">
              {post.content}
            </p>
            
            {/* Sample content sections */}
            <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">
              Understanding the Digital Landscape in Nepal
            </h2>
            <p>
              Nepal&apos;s digital marketing landscape has evolved significantly over the past few years.
              With increasing internet penetration and smartphone adoption, businesses are recognizing 
              the importance of establishing a strong online presence.
            </p>
            
            <h3 className="text-xl font-bold text-gray-900 mt-6 mb-3">
              Key Market Insights
            </h3>
            <ul className="list-disc pl-6 space-y-2">
              <li>Over 18 million internet users in Nepal as of 2024</li>
              <li>Facebook remains the dominant social media platform</li>
              <li>Mobile-first approach is crucial for success</li>
              <li>Local language content performs better</li>
            </ul>
            
            <h3 className="text-xl font-bold text-gray-900 mt-6 mb-3">
              Effective Strategies for Nepali Businesses
            </h3>
            <p>
              To succeed in Nepal&apos;s digital marketing landscape, businesses need to understand
              local consumer behavior, cultural nuances, and platform preferences. Here are 
              some proven strategies that work:
            </p>
            
            <ol className="list-decimal pl-6 space-y-2">
              <li><strong>Localized Content:</strong> Create content in Nepali language when appropriate</li>
              <li><strong>Mobile Optimization:</strong> Ensure all digital assets are mobile-friendly</li>
              <li><strong>Social Media Focus:</strong> Prioritize Facebook and Instagram marketing</li>
              <li><strong>Local SEO:</strong> Optimize for location-based searches</li>
            </ol>
            
            <blockquote className="border-l-4 border-brand-gold pl-6 py-4 bg-gray-50 rounded-r-lg my-8">
              <p className="text-lg italic text-gray-700 mb-2">
                              &quot;Success in digital marketing in Nepal comes from understanding the local market
                and adapting global strategies to fit local preferences and behaviors.&quot;
              </p>
              <cite className="text-brand-navy font-semibold">- Digital Marketing Expert</cite>
            </blockquote>
            
            <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">
              Conclusion
            </h2>
            <p>
              The digital marketing landscape in Nepal presents both opportunities and challenges. 
              By understanding local preferences, leveraging the right platforms, and creating 
              culturally relevant content, businesses can achieve significant growth in the digital space.
            </p>
          </div>
        </div>
      </div>

      {/* Article Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
        className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 mb-8"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center space-x-2 hover:bg-brand-navy hover:text-white"
            >
              <ThumbsUp className="h-4 w-4" />
              <span>Like (24)</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center space-x-2 hover:bg-brand-navy hover:text-white"
            >
              <MessageCircle className="h-4 w-4" />
              <span>Comment (8)</span>
            </Button>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleShare}
              className="flex items-center space-x-2 hover:bg-brand-navy hover:text-white"
            >
              <Share2 className="h-4 w-4" />
              <span>Share</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center space-x-2 hover:bg-brand-gold hover:text-gray-900"
            >
              <Bookmark className="h-4 w-4" />
              <span>Save</span>
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Tags */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100"
      >
        <h3 className="text-lg font-bold text-gray-900 mb-4">Tags</h3>
        <div className="flex flex-wrap gap-2">
          {post.tags.map((tag) => (
            <span
              key={tag}
              className="px-3 py-1 bg-brand-navy/10 text-brand-navy rounded-full text-sm font-medium hover:bg-brand-navy hover:text-white transition-colors cursor-pointer"
            >
              #{tag}
            </span>
          ))}
        </div>
      </motion.div>
    </motion.article>
  );
}
