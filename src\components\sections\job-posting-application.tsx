'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Send, Upload, FileText, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface JobPosting {
  id: string;
  slug: string;
  title: string;
  department: string;
  location: string;
  type: 'full-time' | 'part-time' | 'contract' | 'internship';
  status: 'active' | 'closed' | 'draft';
  publishedAt: string;
  updatedAt?: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
  salary?: {
    min: number;
    max: number;
    currency: string;
    period: string;
  };
  duration?: string;
}

interface JobPostingApplicationProps {
  job: JobPosting;
}

const applicationSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Please enter a valid phone number'),
  experience: z.string().min(1, 'Please select your experience level'),
  coverLetter: z.string().min(50, 'Cover letter must be at least 50 characters'),
  portfolio: z.string().url().optional().or(z.literal('')),
  linkedin: z.string().url().optional().or(z.literal('')),
});

type ApplicationFormData = z.infer<typeof applicationSchema>;

export default function JobPostingApplication({ job }: JobPostingApplicationProps) {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resumeFile, setResumeFile] = useState<File | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ApplicationFormData>({
    resolver: zodResolver(applicationSchema),
  });

  const onSubmit = async (data: ApplicationFormData) => {
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('Application submitted:', {
        ...data,
        jobId: job.id,
        jobTitle: job.title,
        resume: resumeFile?.name,
      });

      setIsSubmitted(true);
      reset();
      setResumeFile(null);
    } catch (error) {
      console.error('Error submitting application:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type and size
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      const maxSize = 5 * 1024 * 1024; // 5MB

      if (!allowedTypes.includes(file.type)) {
        alert('Please upload a PDF or Word document');
        return;
      }

      if (file.size > maxSize) {
        alert('File size must be less than 5MB');
        return;
      }

      setResumeFile(file);
    }
  };

  if (isSubmitted) {
    return (
      <motion.div
        id="application-form"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6 }}
        className="bg-green-50 rounded-2xl p-8 text-center"
      >
        <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-6" />
        <h3 className="text-2xl font-bold text-gray-900 mb-4">Application Submitted!</h3>
        <p className="text-gray-600 mb-6">
          Thank you for your interest in the {job.title} position. We&apos;ve received your application
          and will review it carefully. You can expect to hear from us within 5-7 business days.
        </p>
        <div className="bg-white rounded-lg p-4 border border-green-200">
          <p className="text-sm text-gray-600">
            <strong>Next Steps:</strong> Our HR team will review your application and reach out
            if your profile matches our requirements. In the meantime, feel free to explore
            other opportunities on our careers page.
          </p>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      id="application-form"
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-white rounded-2xl p-8 shadow-lg"
    >
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Apply for {job.title}</h2>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Personal Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="firstName">First Name *</Label>
            <Input
              id="firstName"
              {...register('firstName')}
              className="mt-1"
              placeholder="Enter your first name"
            />
            {errors.firstName && (
              <p className="text-red-500 text-sm mt-1">{errors.firstName.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="lastName">Last Name *</Label>
            <Input
              id="lastName"
              {...register('lastName')}
              className="mt-1"
              placeholder="Enter your last name"
            />
            {errors.lastName && (
              <p className="text-red-500 text-sm mt-1">{errors.lastName.message}</p>
            )}
          </div>
        </div>

        {/* Contact Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="email">Email Address *</Label>
            <Input
              id="email"
              type="email"
              {...register('email')}
              className="mt-1"
              placeholder="<EMAIL>"
            />
            {errors.email && (
              <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="phone">Phone Number *</Label>
            <Input
              id="phone"
              {...register('phone')}
              className="mt-1"
              placeholder="+977 98XXXXXXXX"
            />
            {errors.phone && (
              <p className="text-red-500 text-sm mt-1">{errors.phone.message}</p>
            )}
          </div>
        </div>

        {/* Experience Level */}
        <div>
          <Label htmlFor="experience">Years of Experience *</Label>
          <select
            id="experience"
            {...register('experience')}
            className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-navy focus:border-transparent"
          >
            <option value="">Select your experience level</option>
            <option value="0-1">0-1 years (Entry Level)</option>
            <option value="2-3">2-3 years</option>
            <option value="4-5">4-5 years</option>
            <option value="6-8">6-8 years</option>
            <option value="9+">9+ years (Senior Level)</option>
          </select>
          {errors.experience && (
            <p className="text-red-500 text-sm mt-1">{errors.experience.message}</p>
          )}
        </div>

        {/* Resume Upload */}
        <div>
          <Label htmlFor="resume">Resume/CV *</Label>
          <div className="mt-1">
            <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
              <div className="flex flex-col items-center justify-center pt-5 pb-6">
                {resumeFile ? (
                  <>
                    <FileText className="w-8 h-8 mb-2 text-green-500" />
                    <p className="text-sm text-gray-600">{resumeFile.name}</p>
                    <p className="text-xs text-gray-500">Click to change file</p>
                  </>
                ) : (
                  <>
                    <Upload className="w-8 h-8 mb-2 text-gray-400" />
                    <p className="text-sm text-gray-600">Upload your resume</p>
                    <p className="text-xs text-gray-500">PDF, DOC, DOCX (Max 5MB)</p>
                  </>
                )}
              </div>
              <input
                id="resume"
                type="file"
                className="hidden"
                accept=".pdf,.doc,.docx"
                onChange={handleFileChange}
                required
              />
            </label>
          </div>
        </div>

        {/* Cover Letter */}
        <div>
          <Label htmlFor="coverLetter">Cover Letter *</Label>
          <Textarea
            id="coverLetter"
            {...register('coverLetter')}
            className="mt-1"
            rows={6}
            placeholder="Tell us why you're interested in this position and what makes you a great fit for our team..."
          />
          {errors.coverLetter && (
            <p className="text-red-500 text-sm mt-1">{errors.coverLetter.message}</p>
          )}
        </div>

        {/* Optional Links */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="portfolio">Portfolio URL (Optional)</Label>
            <Input
              id="portfolio"
              {...register('portfolio')}
              className="mt-1"
              placeholder="https://yourportfolio.com"
            />
            {errors.portfolio && (
              <p className="text-red-500 text-sm mt-1">{errors.portfolio.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="linkedin">LinkedIn Profile (Optional)</Label>
            <Input
              id="linkedin"
              {...register('linkedin')}
              className="mt-1"
              placeholder="https://linkedin.com/in/yourprofile"
            />
            {errors.linkedin && (
              <p className="text-red-500 text-sm mt-1">{errors.linkedin.message}</p>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <div className="pt-6">
          <Button
            type="submit"
            size="lg"
            disabled={isSubmitting || !resumeFile}
            className="w-full bg-brand-navy hover:bg-brand-navy-dark text-white font-semibold"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Submitting Application...
              </>
            ) : (
              <>
                Submit Application
                <Send className="ml-2 h-4 w-4" />
              </>
            )}
          </Button>
        </div>

        {/* Privacy Notice */}
        <div className="text-xs text-gray-500 bg-gray-50 p-4 rounded-lg">
          <p>
            By submitting this application, you agree to our privacy policy and consent to the
            processing of your personal data for recruitment purposes. Your information will be
            kept confidential and used solely for evaluating your application.
          </p>
        </div>
      </form>
    </motion.div>
  );
}
