'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRight, Phone, Mail, MessageSquare, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

const benefits = [
  'Free 30-minute consultation',
  'Customized strategy proposal',
  'No obligation commitment',
  'Expert market insights',
  'Transparent pricing',
  'Proven track record'
];

const contactMethods = [
  {
    icon: Phone,
    title: 'Call Us',
    description: 'Speak directly with our experts',
    action: '+977-1-4441234',
    href: 'tel:+97714441234'
  },
  {
    icon: Mail,
    title: 'Email Us',
    description: 'Get detailed information via email',
    action: '<EMAIL>',
    href: 'mailto:<EMAIL>'
  },
  {
    icon: MessageSquare,
    title: 'WhatsApp',
    description: 'Quick chat on WhatsApp',
    action: '+977-9841234567',
    href: 'https://wa.me/9779841234567'
  }
];

export default function ServicesCTA() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Main CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="bg-gradient-to-r from-brand-navy to-brand-navy-dark rounded-3xl p-8 md:p-12 text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Transform Your
              <span className="block text-brand-gold">Digital Presence?</span>
            </h2>
            <p className="text-blue-100 text-lg mb-8 max-w-2xl mx-auto">
              Don&apos;t let your competitors get ahead. Start your digital transformation 
              journey today with Nepal&apos;s most trusted digital marketing agency.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button 
                asChild 
                size="lg" 
                className="bg-brand-gold hover:bg-brand-gold-dark text-gray-900 font-semibold px-8 py-4"
              >
                <Link href="/contact">
                  Get Free Consultation
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button 
                asChild 
                variant="outline" 
                size="lg" 
                className="border-white hover:bg-white text-brand-navy px-8 py-4"
              >
                <Link href="/portfolio">
                  View Our Work
                </Link>
              </Button>
            </div>

            {/* Benefits Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={benefit}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-center space-x-2 text-sm text-blue-100"
                >
                  <CheckCircle className="h-4 w-4 text-brand-gold flex-shrink-0" />
                  <span>{benefit}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Contact Methods */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Multiple Ways to Get Started
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Choose the communication method that works best for you. 
              Our team is ready to help you succeed.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {contactMethods.map((method, index) => (
              <motion.div
                key={method.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-gradient-to-br from-brand-navy to-brand-gold rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                      <method.icon className="h-8 w-8 text-white" />
                    </div>
                    
                    <h4 className="text-xl font-bold text-gray-900 mb-3">
                      {method.title}
                    </h4>
                    
                    <p className="text-gray-600 mb-6">
                      {method.description}
                    </p>
                    
                    <Button 
                      asChild 
                      variant="outline" 
                      className="w-full group-hover:bg-brand-navy group-hover:text-white group-hover:border-brand-navy transition-all duration-300"
                    >
                      <a href={method.href} target="_blank" rel="noopener noreferrer">
                        {method.action}
                      </a>
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* FAQ Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-white rounded-3xl p-8 md:p-12"
        >
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Get quick answers to common questions about our digital marketing services.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {[
              {
                question: 'How long does it take to see results?',
                answer: 'Most clients see initial improvements within 30-60 days, with significant results typically visible within 3-6 months depending on the service.'
              },
              {
                question: 'Do you work with small businesses?',
                answer: 'Absolutely! We specialize in helping small and medium businesses in Nepal grow their digital presence with affordable, effective strategies.'
              },
              {
                question: 'What makes you different from other agencies?',
                answer: 'Our deep understanding of the Nepali market, transparent pricing, proven track record, and dedicated local team set us apart.'
              },
              {
                question: 'Can I customize a service package?',
                answer: 'Yes! We offer flexible packages and can create custom solutions tailored to your specific business needs and budget.'
              }
            ].map((faq, index) => (
              <motion.div
                key={faq.question}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="border-l-4 border-brand-gold pl-6"
              >
                <h4 className="text-lg font-semibold text-gray-900 mb-3">
                  {faq.question}
                </h4>
                <p className="text-gray-600 leading-relaxed">
                  {faq.answer}
                </p>
              </motion.div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 mb-6">
              Have more questions? We&apos;d love to help!
            </p>
            <Button asChild className="bg-brand-navy hover:bg-brand-navy-dark">
              <Link href="/contact">
                Contact Our Experts
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
