'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { Calendar, TrendingUp, User, ArrowRight } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface BlogPost {
  id: string;
  slug: string;
  title: string;
  author: {
    name: string;
    avatar: string;
    bio: string;
  };
  publishedAt: string;
  category: {
    name: string;
    slug: string;
  };
  featuredImage: string;
  readingTime: number;
}

interface BlogPostSidebarProps {
  post: BlogPost;
  allPosts: BlogPost[];
}

export default function BlogPostSidebar({ post, allPosts }: BlogPostSidebarProps) {
  // Get recent posts (excluding current post)
  const recentPosts = allPosts
    .filter(p => p.id !== post.id)
    .slice(0, 5);

  // Get popular posts (mock data - in real app would be based on views/engagement)
  const popularPosts = allPosts
    .filter(p => p.id !== post.id)
    .slice(0, 3);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <div className="space-y-8">
      {/* Author Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Card className="border-0 shadow-lg">
          <CardHeader className="text-center pb-4">
            <div className="w-20 h-20 mx-auto mb-4">
              <Image
                src={post.author.avatar}
                alt={post.author.name}
                width={80}
                height={80}
                className="rounded-full object-cover"
              />
            </div>
            <CardTitle className="text-xl text-gray-900">{post.author.name}</CardTitle>
            <p className="text-gray-600 text-sm">{post.author.bio}</p>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-center">
              <Link href="/about#team">
                <button className="w-full px-4 py-2 bg-brand-navy text-white rounded-lg hover:bg-brand-navy-dark transition-colors">
                  View Profile
                </button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Table of Contents */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="text-lg text-gray-900">Table of Contents</CardTitle>
          </CardHeader>
          <CardContent>
            <nav className="space-y-2">
              <a href="#understanding" className="block text-sm text-gray-600 hover:text-brand-navy transition-colors py-1">
                Understanding the Digital Landscape
              </a>
              <a href="#insights" className="block text-sm text-gray-600 hover:text-brand-navy transition-colors py-1 pl-4">
                Key Market Insights
              </a>
              <a href="#strategies" className="block text-sm text-gray-600 hover:text-brand-navy transition-colors py-1 pl-4">
                Effective Strategies
              </a>
              <a href="#conclusion" className="block text-sm text-gray-600 hover:text-brand-navy transition-colors py-1">
                Conclusion
              </a>
            </nav>
          </CardContent>
        </Card>
      </motion.div>

      {/* Recent Posts */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="text-lg text-gray-900 flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-brand-gold" />
              Recent Posts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentPosts.map((recentPost) => (
                <Link key={recentPost.id} href={`/blog/${recentPost.slug}`}>
                  <div className="group flex space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                    <div className="flex-shrink-0">
                      <Image
                        src={recentPost.featuredImage}
                        alt={recentPost.title}
                        width={60}
                        height={60}
                        className="rounded-lg object-cover"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-semibold text-gray-900 group-hover:text-brand-navy transition-colors line-clamp-2">
                        {recentPost.title}
                      </h4>
                      <div className="flex items-center text-xs text-gray-500 mt-1">
                        <span>{formatDate(recentPost.publishedAt)}</span>
                        <span className="mx-1">•</span>
                        <span>{recentPost.readingTime} min</span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Popular Posts */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="text-lg text-gray-900 flex items-center">
              <TrendingUp className="h-5 w-5 mr-2 text-brand-gold" />
              Popular Posts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {popularPosts.map((popularPost, index) => (
                <Link key={popularPost.id} href={`/blog/${popularPost.slug}`}>
                  <div className="group p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                    <div className="flex items-start space-x-2 mb-2">
                      <span className="flex-shrink-0 w-6 h-6 bg-brand-gold text-gray-900 rounded-full flex items-center justify-center text-xs font-bold">
                        {index + 1}
                      </span>
                      <h4 className="text-sm font-semibold text-gray-900 group-hover:text-brand-navy transition-colors line-clamp-2">
                        {popularPost.title}
                      </h4>
                    </div>
                    <div className="flex items-center text-xs text-gray-500 ml-8">
                      <Badge variant="outline" className="text-xs">
                        {popularPost.category.name}
                      </Badge>
                      <span className="mx-2">•</span>
                      <span>{popularPost.readingTime} min read</span>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Newsletter CTA */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <Card className="border-0 shadow-lg bg-gradient-to-br from-brand-navy to-brand-navy-dark text-white">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-brand-gold rounded-full flex items-center justify-center mx-auto mb-4">
              <User className="h-6 w-6 text-gray-900" />
            </div>
            <h3 className="text-lg font-bold mb-2">Stay Updated</h3>
            <p className="text-blue-100 text-sm mb-4">
              Get the latest digital marketing insights delivered to your inbox.
            </p>
            <Link href="/blog#newsletter">
              <button className="w-full px-4 py-2 bg-brand-gold text-gray-900 rounded-lg hover:bg-brand-gold/90 transition-colors font-semibold">
                Subscribe Now
              </button>
            </Link>
          </CardContent>
        </Card>
      </motion.div>

      {/* Contact CTA */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.5 }}
      >
        <Card className="border-0 shadow-lg">
          <CardContent className="p-6 text-center">
            <h3 className="text-lg font-bold text-gray-900 mb-2">Need Help?</h3>
            <p className="text-gray-600 text-sm mb-4">
              Ready to implement these strategies for your business?
            </p>
            <Link href="/contact">
              <button className="w-full px-4 py-2 bg-brand-navy text-white rounded-lg hover:bg-brand-navy-dark transition-colors font-semibold flex items-center justify-center space-x-2">
                <span>Get Free Consultation</span>
                <ArrowRight className="h-4 w-4" />
              </button>
            </Link>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
