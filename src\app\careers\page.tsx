import { Metadata } from 'next';
import CareersHero from '@/components/sections/careers-hero';
import WhyWorkWithUs from '@/components/sections/why-work-with-us';
import OpenPositions from '@/components/sections/open-positions';
import CompanyBenefits from '@/components/sections/company-benefits';
import CareersCTA from '@/components/sections/careers-cta';

export const metadata: Metadata = {
  title: 'Careers - Join Our Team at Lunar Cubes Nepal',
  description: 'Join the leading digital marketing agency in Nepal. Explore exciting career opportunities in web development, digital marketing, design, and more. Build your career with Lunar Cubes.',
  keywords: 'careers Nepal, jobs Kathmandu, digital marketing jobs Nepal, web developer jobs Nepal, UI UX designer jobs Nepal, marketing jobs Kathmandu, tech jobs Nepal',
  openGraph: {
    title: 'Careers - Join Our Team at Lunar Cubes Nepal',
    description: 'Build your career with Nepal\'s leading digital marketing agency. Explore exciting opportunities and grow with us.',
    url: '/careers',
  },
};

export default function CareersPage() {
  return (
    <div>
      <CareersHero />
      <WhyWorkWithUs />
      <OpenPositions />
      <CompanyBenefits />
      <CareersCTA />
    </div>
  );
}
