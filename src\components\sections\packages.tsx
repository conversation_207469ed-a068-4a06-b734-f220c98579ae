'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { <PERSON>, Star, ArrowRight, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { usePackages } from '@/lib/queries/hooks';
import { Loading } from '@/components/ui/loading';

export default function Packages() {
  const { data: packages, isLoading, error } = usePackages();

  if (isLoading) {
    return (
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <Loading size="lg" text="Loading packages..." />
          </div>
        </div>
      </section>
    );
  }

  if (error || !packages) {
    return null;
  }

  // Show only the main packages (first 3)
  const mainPackages = packages.slice(0, 3);

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center px-4 py-2 bg-brand-navy/10 text-brand-navy rounded-full text-sm font-medium mb-6">
            <Zap className="w-4 h-4 mr-2 text-brand-gold" />
            Pricing Packages
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Choose Your Growth
            <span className="block text-brand-navy">Package</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Transparent pricing with no hidden costs. Choose the package that fits your business 
            needs and budget. All packages include our signature Nepali market expertise.
          </p>
        </motion.div>

        {/* Packages Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {mainPackages.map((pkg, index) => (
            <motion.div
              key={pkg.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="relative"
            >
              <Card className={`h-full transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 ${
                pkg.popular 
                  ? 'border-2 border-brand-gold shadow-xl scale-105' 
                  : 'border border-gray-200 shadow-md'
              }`}>
                {pkg.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-brand-gold text-gray-900 px-4 py-1 text-sm font-semibold">
                      <Star className="w-4 h-4 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-4">
                  <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                    {pkg.name}
                  </CardTitle>
                  <CardDescription className="text-gray-600 mb-6">
                    {pkg.description}
                  </CardDescription>
                  
                  <div className="mb-6">
                    <div className="text-4xl font-bold text-brand-navy mb-2">
                      NPR {pkg.price.toLocaleString()}
                      <span className="text-lg font-normal text-gray-500">/{pkg.duration.toLowerCase()}</span>
                    </div>
                    {pkg.popular && (
                      <div className="text-sm text-brand-gold font-medium">
                        Save 20% compared to individual services
                      </div>
                    )}
                  </div>
                </CardHeader>

                <CardContent className="pt-0">
                  {/* Features List */}
                  <div className="space-y-3 mb-8">
                    {pkg.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-start">
                        <Check className="h-5 w-5 text-brand-gold mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 text-sm leading-relaxed">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* Ideal For */}
                  <div className="mb-8">
                    <h4 className="text-sm font-semibold text-gray-900 mb-3">Ideal for:</h4>
                    <div className="flex flex-wrap gap-2">
                      {pkg.idealFor.map((ideal, idealIndex) => (
                        <Badge 
                          key={idealIndex} 
                          variant="secondary" 
                          className="text-xs bg-gray-100 text-gray-700"
                        >
                          {ideal}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* CTA Button */}
                  <Button 
                    asChild 
                    className={`w-full ${
                      pkg.popular
                        ? 'bg-brand-gold hover:bg-brand-gold-dark text-gray-900'
                        : 'bg-brand-navy hover:bg-brand-navy-dark text-white'
                    }`}
                  >
                    <Link href="/contact">
                      Get Started
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>

                  <div className="text-center mt-4">
                    <Link 
                      href="/packages" 
                      className="text-sm text-brand-navy hover:underline"
                    >
                      View detailed features
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <div className="bg-gray-50 rounded-3xl p-8 md:p-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Need a Custom Solution?
            </h3>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
              Every business is unique. If our standard packages don&apos;t fit your specific needs, 
              we&apos;ll create a custom solution tailored to your goals and budget.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              {[
                {
                  title: 'Free Consultation',
                  description: 'Discuss your goals and challenges with our experts'
                },
                {
                  title: 'Custom Proposal',
                  description: 'Receive a tailored strategy and pricing proposal'
                },
                {
                  title: 'Flexible Terms',
                  description: 'Choose payment terms that work for your business'
                }
              ].map((step, index) => (
                <div key={step.title} className="text-center">
                  <div className="w-12 h-12 bg-brand-navy text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                    {index + 1}
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">{step.title}</h4>
                  <p className="text-gray-600 text-sm">{step.description}</p>
                </div>
              ))}
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-brand-navy hover:bg-brand-navy-dark">
                <Link href="/contact">
                  Schedule Free Consultation
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg">
                <Link href="/packages">
                  View All Packages
                </Link>
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
