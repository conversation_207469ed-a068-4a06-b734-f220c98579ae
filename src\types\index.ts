// Core business types for Lunar Cubes Digital Marketing Agency

export interface Service {
  id: string;
  title: string;
  description: string;
  shortDescription: string;
  icon: string;
  image?: string;
  featured?: boolean;
  features: string[];
  caseStudy?: CaseStudy;
  pricing?: {
    basic: number;
    standard: number;
    premium: number;
  };
}

export interface CaseStudy {
  id: string;
  title: string;
  client: string;
  industry: string;
  challenge: string;
  solution: string;
  results: string[];
  image: string;
  duration: string;
  serviceUsed: string;
}

export interface TeamMember {
  id: string;
  name: string;
  position: string;
  bio: string;
  image: string;
  expertise: string[];
  experience: string;
  education?: string;
  social?: {
    linkedin?: string;
    twitter?: string;
    email?: string;
  };
}

export interface Testimonial {
  id: string;
  name: string;
  position: string;
  company: string;
  content: string;
  rating: number;
  image: string;
  serviceUsed: string;
  location: string;
}

export interface Portfolio {
  id: string;
  title: string;
  client: string;
  category: string;
  description: string;
  image: string;
  results: {
    metric: string;
    value: string;
    improvement: string;
  }[];
  technologies: string[];
  duration: string;
  url?: string;
}

export interface Package {
  id: string;
  name: string;
  price: number;
  duration: string;
  description: string;
  features: string[];
  popular?: boolean;
  services: string[];
  idealFor: string[];
}

export interface Industry {
  id: string;
  name: string;
  description: string;
  icon: string;
  services: string[];
  caseStudies: string[];
  challenges: string[];
}

export interface ContactForm {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  service: string;
  budget: string;
  message: string;
  timeline: string;
}

export interface LeadForm {
  name: string;
  email: string;
  phone?: string;
  service: string;
  company?: string;
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  author: {
    name: string;
    avatar: string;
    bio: string;
  };
  publishedAt: string;
  updatedAt: string;
  category: {
    name: string;
    slug: string;
  };
  tags: string[];
  featuredImage: string;
  readingTime: number;
  seo: {
    metaTitle: string;
    metaDescription: string;
    keywords: string[];
    canonicalUrl: string;
  };
  featured: boolean;
  status: string;
}

export interface CompanyInfo {
  name: string;
  tagline: string;
  description: string;
  mission: string;
  vision: string;
  founded: string;
  location: {
    address: string;
    city: string;
    country: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  };
  contact: {
    phone: string;
    email: string;
    whatsapp?: string;
  };
  social: {
    facebook?: string;
    instagram?: string;
    linkedin?: string;
    twitter?: string;
  };
  stats: {
    clientsServed: number;
    projectsCompleted: number;
    yearsExperience: number;
    teamMembers: number;
  };
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Job Posting types
export interface JobPosting {
  id: string;
  slug: string;
  title: string;
  department: string;
  location: string;
  type: 'full-time' | 'part-time' | 'contract' | 'internship';
  status: 'active' | 'closed' | 'draft';
  publishedAt: string;
  updatedAt?: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
  salary?: {
    min: number;
    max: number;
    currency: string;
    period: string;
  };
  duration?: string;
}
