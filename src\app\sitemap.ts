import { MetadataRoute } from "next";
import fs from "fs";
import path from "path";

interface BlogPost {
  id: string;
  slug: string;
  title: string;
  publishedAt: string;
  updatedAt?: string;
  status: "published" | "draft";
}

interface PortfolioItem {
  id: string;
  title: string;
}

interface JobPosting {
  id: string;
  slug: string;
  title: string;
  department: string;
  location: string;
  type: "full-time" | "part-time" | "contract" | "internship";
  status: "active" | "closed" | "draft";
  publishedAt: string;
  updatedAt?: string;
}

/**
 * Get blog posts from data file
 */
function getBlogPosts(): BlogPost[] {
  try {
    const blogPostsPath = path.join(process.cwd(), "public/data/blog-posts.json");
    if (!fs.existsSync(blogPostsPath)) {
      return [];
    }
    const data = fs.readFileSync(blogPostsPath, "utf8");
    const posts = JSON.parse(data) as BlogPost[];
    return posts.filter((post) => post.status === "published");
  } catch (error) {
    console.error("Error reading blog posts:", error);
    return [];
  }
}

/**
 * Get portfolio items from data file
 */
function getPortfolioItems(): PortfolioItem[] {
  try {
    const portfolioPath = path.join(process.cwd(), "public/data/portfolio.json");
    if (!fs.existsSync(portfolioPath)) {
      return [];
    }
    const data = fs.readFileSync(portfolioPath, "utf8");
    return JSON.parse(data) as PortfolioItem[];
  } catch (error) {
    console.error("Error reading portfolio items:", error);
    return [];
  }
}

/**
 * Get job postings from data file
 */
function getJobPostings(): JobPosting[] {
  try {
    const jobsPath = path.join(process.cwd(), "public/data/jobs.json");
    if (!fs.existsSync(jobsPath)) {
      return [];
    }
    const data = fs.readFileSync(jobsPath, "utf8");
    const jobs = JSON.parse(data) as JobPosting[];
    return jobs.filter((job) => job.status === "active");
  } catch (error) {
    console.error("Error reading job postings:", error);
    return [];
  }
}

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://lunarcubes.com.np";
  const currentDate = new Date();

  const sitemap: MetadataRoute.Sitemap = [];

  // Static routes
  const staticRoutes = [
    { url: `${baseUrl}/`, priority: 1.0, changeFrequency: "weekly" as const },
    { url: `${baseUrl}/about`, priority: 0.8, changeFrequency: "monthly" as const },
    { url: `${baseUrl}/services`, priority: 0.9, changeFrequency: "weekly" as const },
    { url: `${baseUrl}/portfolio`, priority: 0.8, changeFrequency: "weekly" as const },
    { url: `${baseUrl}/contact`, priority: 0.7, changeFrequency: "monthly" as const },
    { url: `${baseUrl}/careers`, priority: 0.8, changeFrequency: "weekly" as const },
    { url: `${baseUrl}/blog`, priority: 0.9, changeFrequency: "daily" as const },
  ];

  // Add static routes
  staticRoutes.forEach((route) => {
    sitemap.push({
      url: route.url,
      lastModified: currentDate,
      changeFrequency: route.changeFrequency,
      priority: route.priority,
    });
  });

  // Add blog post routes
  const blogPosts = getBlogPosts();
  blogPosts.forEach((post) => {
    sitemap.push({
      url: `${baseUrl}/blog/${post.slug}`,
      lastModified: new Date(post.updatedAt || post.publishedAt),
      changeFrequency: "weekly",
      priority: 0.7,
    });
  });

  // Add portfolio routes
  const portfolioItems = getPortfolioItems();
  portfolioItems.forEach((item) => {
    sitemap.push({
      url: `${baseUrl}/portfolio/${item.id}`,
      lastModified: currentDate,
      changeFrequency: "monthly",
      priority: 0.6,
    });
  });

  // Add job posting routes
  const jobPostings = getJobPostings();
  jobPostings.forEach((job) => {
    sitemap.push({
      url: `${baseUrl}/careers/${job.slug}`,
      lastModified: new Date(job.updatedAt || job.publishedAt),
      changeFrequency: "weekly",
      priority: 0.6,
    });
  });

  return sitemap;
}
