<svg width="1200" height="600" viewBox="0 0 1200 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="contactGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#eff6ff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fefce8;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="contactCircle1" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:0.12" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:0" />
    </radialGradient>
    <radialGradient id="contactCircle2" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:0.12" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="600" fill="url(#contactGradient)"/>
  
  <!-- Decorative Circles -->
  <circle cx="180" cy="120" r="100" fill="url(#contactCircle1)"/>
  <circle cx="1020" cy="480" r="120" fill="url(#contactCircle2)"/>
  <circle cx="700" cy="250" r="80" fill="url(#contactCircle1)" opacity="0.7"/>
  
  <!-- Phone Icon -->
  <g transform="translate(150, 100)">
    <circle cx="0" cy="0" r="28" fill="#1e40af" opacity="0.1"/>
    <rect x="-8" y="-12" width="16" height="24" rx="3" stroke="#1e40af" stroke-width="2" fill="none"/>
    <rect x="-6" y="-8" width="12" height="1" fill="#1e40af"/>
    <rect x="-6" y="-6" width="12" height="1" fill="#1e40af"/>
    <rect x="-6" y="-4" width="12" height="1" fill="#1e40af"/>
    <circle cx="0" cy="8" r="2" fill="#1e40af"/>
  </g>
  
  <!-- Email Icon -->
  <g transform="translate(1050, 150)">
    <circle cx="0" cy="0" r="28" fill="#f59e0b" opacity="0.1"/>
    <rect x="-12" y="-8" width="24" height="16" rx="2" stroke="#f59e0b" stroke-width="2" fill="none"/>
    <path d="M-12 -8 L0 2 L12 -8" stroke="#f59e0b" stroke-width="2" fill="none"/>
  </g>
  
  <!-- Location Pin Icon -->
  <g transform="translate(250, 450)">
    <circle cx="0" cy="0" r="25" fill="#1e40af" opacity="0.1"/>
    <path d="M0 -12 Q-8 -12 -8 -4 Q-8 4 0 12 Q8 4 8 -4 Q8 -12 0 -12 Z" fill="#1e40af" opacity="0.7"/>
    <circle cx="0" cy="-4" r="3" fill="white"/>
  </g>
  
  <!-- Message/Chat Icon -->
  <g transform="translate(950, 450)">
    <circle cx="0" cy="0" r="25" fill="#f59e0b" opacity="0.1"/>
    <rect x="-10" y="-8" width="20" height="12" rx="3" fill="#f59e0b" opacity="0.7"/>
    <path d="M-5 4 L0 8 L5 4" fill="#f59e0b" opacity="0.7"/>
    <circle cx="-4" cy="-3" r="1" fill="white"/>
    <circle cx="0" cy="-3" r="1" fill="white"/>
    <circle cx="4" cy="-3" r="1" fill="white"/>
  </g>
  
  <!-- Clock Icon -->
  <g transform="translate(600, 400)">
    <circle cx="0" cy="0" r="25" fill="#1e40af" opacity="0.1"/>
    <circle cx="0" cy="0" r="10" stroke="#1e40af" stroke-width="2" fill="none"/>
    <path d="M0 0 L0 -6" stroke="#1e40af" stroke-width="2"/>
    <path d="M0 0 L4 0" stroke="#1e40af" stroke-width="2"/>
  </g>
  
  <!-- Connecting Lines -->
  <path d="M200 150 Q400 100 600 150 Q800 200 1000 180" stroke="#1e40af" stroke-width="1" opacity="0.25" fill="none"/>
  <path d="M300 400 Q600 350 900 420" stroke="#f59e0b" stroke-width="1" opacity="0.25" fill="none"/>
  
  <!-- Floating Elements -->
  <circle cx="100" cy="300" r="3" fill="#1e40af" opacity="0.4"/>
  <circle cx="1100" cy="280" r="2" fill="#f59e0b" opacity="0.4"/>
  <circle cx="500" cy="120" r="2" fill="#1e40af" opacity="0.4"/>
  <circle cx="800" cy="500" r="3" fill="#f59e0b" opacity="0.4"/>
  
  <!-- Nepal Mountains Silhouette -->
  <g transform="translate(0, 450)" opacity="0.1">
    <path d="M0 150 L100 50 L200 100 L300 30 L400 80 L500 20 L600 60 L700 10 L800 40 L900 80 L1000 50 L1100 90 L1200 70 L1200 150 Z" fill="#1e40af"/>
  </g>
</svg>
