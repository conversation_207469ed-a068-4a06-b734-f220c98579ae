'use client';

import { motion } from 'framer-motion';
import { useRouter, useSearchParams } from 'next/navigation';
import { Filter, Grid } from 'lucide-react';

interface Category {
  id: string;
  name: string;
  slug: string;
  description: string;
  color: string;
  postCount: number;
}

interface BlogCategoriesProps {
  categories: Category[];
  activeCategory?: string;
}

export default function BlogCategories({ categories, activeCategory }: BlogCategoriesProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleCategoryClick = (categorySlug: string) => {
    const params = new URLSearchParams(searchParams);
    if (categorySlug === 'all') {
      params.delete('category');
    } else {
      params.set('category', categorySlug);
    }
    params.delete('page'); // Reset to first page
    router.push(`/blog?${params.toString()}`);
  };

  return (
    <section className="py-12 bg-white border-b border-gray-100">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="flex flex-col md:flex-row md:items-center md:justify-between mb-8"
        >
          <div>
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
              Browse by Category
            </h2>
            <p className="text-gray-600">
              Find articles that match your interests and business needs
            </p>
          </div>
          
          <div className="flex items-center space-x-2 mt-4 md:mt-0">
            <Filter className="h-5 w-5 text-gray-400" />
            <span className="text-sm text-gray-500">Filter & Sort</span>
          </div>
        </motion.div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-8">
          {/* All Categories */}
          <motion.button
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            onClick={() => handleCategoryClick('all')}
            className={`group p-6 rounded-2xl border-2 transition-all duration-300 text-left hover:shadow-lg hover:-translate-y-1 ${
              !activeCategory
                ? 'border-brand-navy bg-brand-navy text-white'
                : 'border-gray-200 bg-white text-gray-900 hover:border-brand-navy/30'
            }`}
          >
            <div className="flex items-center justify-between mb-3">
              <Grid className={`h-6 w-6 ${!activeCategory ? 'text-brand-gold' : 'text-brand-navy'}`} />
              <span className={`text-sm font-medium px-2 py-1 rounded-full ${
                !activeCategory 
                  ? 'bg-brand-gold/20 text-brand-gold' 
                  : 'bg-brand-navy/10 text-brand-navy'
              }`}>
                All
              </span>
            </div>
            <h3 className="font-bold text-lg mb-2">All Articles</h3>
            <p className={`text-sm ${!activeCategory ? 'text-blue-100' : 'text-gray-600'}`}>
              Browse all our expert insights and tips
            </p>
          </motion.button>

          {/* Individual Categories */}
          {categories.map((category, index) => (
            <motion.button
              key={category.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              onClick={() => handleCategoryClick(category.slug)}
              className={`group p-6 rounded-2xl border-2 transition-all duration-300 text-left hover:shadow-lg hover:-translate-y-1 ${
                activeCategory === category.slug
                  ? 'border-brand-navy bg-brand-navy text-white'
                  : 'border-gray-200 bg-white text-gray-900 hover:border-brand-navy/30'
              }`}
            >
              <div className="flex items-center justify-between mb-3">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: category.color }}
                ></div>
                <span className={`text-sm font-medium px-2 py-1 rounded-full ${
                  activeCategory === category.slug 
                    ? 'bg-brand-gold/20 text-brand-gold' 
                    : 'bg-gray-100 text-gray-600'
                }`}>
                  {category.postCount}
                </span>
              </div>
              <h3 className="font-bold text-lg mb-2">{category.name}</h3>
              <p className={`text-sm ${
                activeCategory === category.slug ? 'text-blue-100' : 'text-gray-600'
              }`}>
                {category.description}
              </p>
            </motion.button>
          ))}
        </div>

        {/* Active Filter Display */}
        {activeCategory && (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4 }}
            className="flex items-center space-x-3 p-4 bg-brand-navy/5 rounded-xl border border-brand-navy/20"
          >
            <Filter className="h-5 w-5 text-brand-navy" />
            <span className="text-brand-navy font-medium">
              Showing articles in: 
            </span>
            <span className="px-3 py-1 bg-brand-navy text-white rounded-full text-sm font-medium">
              {categories.find(cat => cat.slug === activeCategory)?.name}
            </span>
            <button
              onClick={() => handleCategoryClick('all')}
              className="text-brand-navy hover:text-brand-navy-dark text-sm font-medium underline"
            >
              Clear filter
            </button>
          </motion.div>
        )}
      </div>
    </section>
  );
}
