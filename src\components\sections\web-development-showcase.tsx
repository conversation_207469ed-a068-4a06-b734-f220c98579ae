'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRight, Code, Smartphone, Globe, Zap, Shield, Palette } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const technologies = [
  { name: 'React', icon: '⚛️', color: 'bg-blue-100 text-blue-800' },
  { name: 'Next.js', icon: '▲', color: 'bg-gray-100 text-gray-800' },
  { name: 'WordPress', icon: '🔧', color: 'bg-blue-100 text-blue-800' },
  { name: 'Node.js', icon: '🟢', color: 'bg-green-100 text-green-800' },
  { name: 'MongoDB', icon: '🍃', color: 'bg-green-100 text-green-800' },
  { name: 'Tailwind CSS', icon: '🎨', color: 'bg-cyan-100 text-cyan-800' },
];

const webFeatures = [
  {
    icon: Code,
    title: 'Custom Development',
    description: 'Tailored solutions built from scratch using modern frameworks and technologies.'
  },
  {
    icon: Smartphone,
    title: 'Mobile-First Design',
    description: 'Responsive designs that work perfectly on all devices and screen sizes.'
  },
  {
    icon: Globe,
    title: 'SEO Optimized',
    description: 'Built-in SEO best practices to help your website rank higher on search engines.'
  },
  {
    icon: Zap,
    title: 'Lightning Fast',
    description: 'Optimized for speed with modern performance techniques and CDN integration.'
  },
  {
    icon: Shield,
    title: 'Secure & Reliable',
    description: 'Enterprise-level security measures and reliable hosting solutions.'
  },
  {
    icon: Palette,
    title: 'Beautiful UI/UX',
    description: 'Stunning designs that provide exceptional user experience and engagement.'
  },
];

export default function WebDevelopmentShowcase() {
  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge className="bg-brand-gold text-gray-900 mb-4 text-sm px-4 py-2">
            🚀 Our Flagship Service
          </Badge>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Web Development
            <span className="block text-brand-navy">Excellence</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Transform your business with cutting-edge web solutions. We create stunning, 
            high-performance websites and e-commerce platforms that drive real results 
            for businesses across Nepal and beyond.
          </p>
        </motion.div>

        {/* Main Showcase */}
        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Why Choose Our Web Development Services?
            </h3>
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              With over 5 years of experience and 200+ successful projects, we&apos;re Nepal&apos;s
              leading web development agency. We combine global best practices with local
              market understanding to deliver exceptional results.
            </p>

            {/* Key Stats */}
            <div className="grid grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-brand-navy">200+</div>
                <div className="text-sm text-gray-600">Websites Built</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-brand-navy">99%</div>
                <div className="text-sm text-gray-600">Client Satisfaction</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-brand-navy">24/7</div>
                <div className="text-sm text-gray-600">Support</div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                asChild 
                size="lg"
                className="bg-brand-navy hover:bg-brand-navy-dark text-white"
              >
                <Link href="/contact">
                  Start Your Project
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button 
                asChild 
                variant="outline" 
                size="lg"
                className="border-brand-navy text-brand-navy hover:bg-brand-navy hover:text-white"
              >
                <Link href="/portfolio">
                  View Our Work
                </Link>
              </Button>
            </div>
          </motion.div>

          {/* Visual */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative h-96 rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Web Development"
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-brand-navy/80 to-transparent" />
              <div className="absolute bottom-6 left-6 right-6">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white text-sm">Project Success Rate</span>
                    <span className="text-brand-gold font-bold">99.2%</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div className="bg-brand-gold h-2 rounded-full w-[99%]"></div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-12">
            What Makes Our Web Development Special
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {webFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <CardHeader>
                    <div className="w-12 h-12 bg-brand-gold/10 rounded-xl flex items-center justify-center mb-4">
                      <feature.icon className="w-6 h-6 text-brand-gold" />
                    </div>
                    <CardTitle className="text-xl text-gray-900">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">{feature.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Technologies */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h3 className="text-3xl font-bold text-gray-900 mb-8">
            Technologies We Master
          </h3>
          <div className="flex flex-wrap justify-center gap-4">
            {technologies.map((tech, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Badge className={`${tech.color} text-lg px-4 py-2 flex items-center gap-2`}>
                  <span>{tech.icon}</span>
                  {tech.name}
                </Badge>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
