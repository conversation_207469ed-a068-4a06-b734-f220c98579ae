'use client';

import { motion } from 'framer-motion';
import { Shield, Target, Users, Zap, Heart, Award } from 'lucide-react';
import { useCompanyInfo } from '@/lib/queries/hooks';

const values = [
  {
    icon: Shield,
    title: 'Integrity & Transparency',
    description: 'We believe in honest communication, clear reporting, and always putting our clients\' best interests first. No hidden costs, no false promises.',
    color: 'from-blue-500 to-blue-600'
  },
  {
    icon: Target,
    title: 'Results-Driven Approach',
    description: 'Every strategy we create is focused on delivering measurable results that directly impact your business growth and success.',
    color: 'from-green-500 to-green-600'
  },
  {
    icon: Users,
    title: 'Client Partnership',
    description: 'We see ourselves as partners in your success, working closely with you to understand your goals and challenges.',
    color: 'from-purple-500 to-purple-600'
  },
  {
    icon: Zap,
    title: 'Innovation & Excellence',
    description: 'We stay ahead of digital marketing trends and continuously improve our strategies to keep our clients competitive.',
    color: 'from-yellow-500 to-orange-500'
  },
  {
    icon: Heart,
    title: 'Local Market Focus',
    description: 'Our deep understanding of Nepali culture, consumer behavior, and market dynamics drives our strategic decisions.',
    color: 'from-red-500 to-pink-500'
  },
  {
    icon: Award,
    title: 'Quality Assurance',
    description: 'We maintain the highest standards in everything we do, from strategy development to campaign execution and reporting.',
    color: 'from-indigo-500 to-purple-500'
  }
];

export default function AboutValues() {
  const { data: company } = useCompanyInfo();

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center px-4 py-2 bg-brand-gold/10 text-brand-navy rounded-full text-sm font-medium mb-6">
            Our Values
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            What Drives Us
            <span className="block text-brand-navy">Every Day</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Our core values shape everything we do, from how we work with clients to how we 
            approach digital marketing challenges. These principles guide our decisions and define our culture.
          </p>
        </motion.div>

        {/* Values Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {values.map((value, index) => (
            <motion.div
              key={value.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-gray-100 h-full">
                <div className={`w-16 h-16 bg-gradient-to-r ${value.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <value.icon className="h-8 w-8 text-white" />
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-brand-navy transition-colors">
                  {value.title}
                </h3>
                
                <p className="text-gray-600 leading-relaxed">
                  {value.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Vision Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-gray-50 rounded-3xl p-8 md:p-12"
        >
          <div className="max-w-4xl mx-auto text-center">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
              Our Vision for Nepal&apos;s Digital Future
            </h3>
            {company && (
              <p className="text-lg text-gray-700 mb-8 leading-relaxed">
                {company.vision}
              </p>
            )}
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-brand-navy rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Target className="h-8 w-8 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">Empower Businesses</h4>
                <p className="text-gray-600 text-sm">
                  Help every Nepali business reach its full potential in the digital world
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-brand-gold rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-8 w-8 text-gray-900" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">Drive Innovation</h4>
                <p className="text-gray-600 text-sm">
                  Bring cutting-edge digital marketing strategies to the Nepali market
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-brand-navy to-brand-gold rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Heart className="h-8 w-8 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">Build Community</h4>
                <p className="text-gray-600 text-sm">
                  Create a thriving ecosystem of digitally empowered Nepali businesses
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Commitment Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16"
        >
          <div className="bg-gradient-to-r from-brand-navy to-brand-navy-dark rounded-3xl p-8 md:p-12 text-white">
            <div className="max-w-4xl mx-auto text-center">
              <h3 className="text-2xl md:text-3xl font-bold mb-6">
                Our Commitment to You
              </h3>
              <p className="text-blue-100 text-lg mb-8 leading-relaxed">
                When you choose Lunar Cubes, you&apos;re not just hiring a digital marketing agency. 
                You&apos;re partnering with a team that is genuinely invested in your success and 
                committed to helping your business thrive in Nepal&apos;s digital landscape.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6">
                  <h4 className="text-xl font-bold mb-3">24/7 Support</h4>
                  <p className="text-blue-100">
                    We&apos;re always here when you need us, providing ongoing support and guidance 
                    for your digital marketing journey.
                  </p>
                </div>
                
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6">
                  <h4 className="text-xl font-bold mb-3">Continuous Learning</h4>
                  <p className="text-blue-100">
                    We stay updated with the latest trends and technologies to ensure your 
                    strategies remain effective and competitive.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
